<?php

namespace App\Models;

use App\Traits\SettingsTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\Lead
 *
 * @property int $id
 * @property string $email
 * @property string|null $ip_address
 * @property string|null $first_name
 * @property string|null $last_name
 * @property string|null $zip
 * @property string|null $state
 * @property string|null $city
 * @property int $location_id
 * @property float|null $lat
 * @property float|null $lng
 * @property mixed $custom_fields
 * @property int $has_address
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property mixed $settings
 * @property-read \App\Models\Pickup|null $pickup
 * @method static \Illuminate\Database\Eloquent\Builder|Lead newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Lead newQuery()
 * @method static \Illuminate\Database\Query\Builder|Lead onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Lead query()
 * @method static \Illuminate\Database\Eloquent\Builder|Lead whereCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Lead whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Lead whereCustomFields($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Lead whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Lead whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Lead whereFirstName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Lead whereHasAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Lead whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Lead whereIpAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Lead whereLastName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Lead whereLat($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Lead whereLng($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Lead whereLocationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Lead whereState($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Lead whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Lead whereZip($value)
 * @method static \Illuminate\Database\Query\Builder|Lead withTrashed()
 * @method static \Illuminate\Database\Query\Builder|Lead withoutTrashed()
 * @method static \Database\Factories\LeadFactory factory($count = null, $state = [])
 * @mixin \Eloquent
 */
class Lead extends Model
{
    use HasFactory, SettingsTrait, SoftDeletes;

    protected $guarded = [];

    public function pickup(): HasOne
    {
        return $this->hasOne(Pickup::class, 'id', 'location_id');
    }

    public function getMergedCustomFields()
    {
        return collect([
            'email' => $this->email,
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'city' => $this->city,
            'state' => $this->state,
            'postal_code' => $this->zip,
            'location_id' => $this->location_id,
            'lat' => $this->lat,
            'lng' => $this->lng,
        ])->merge($this->custom_fields);
    }

    protected function settingsField(): string
    {
        return 'custom_fields';
    }

    protected function casts(): array
    {
        return [
            'lat' => 'decimal:6',
            'lng' => 'decimal:6',
        ];
    }
}
