<?php

namespace App\Listeners\Order\OrderWasPaid;

use App\Actions\IssueGiftCard;
use App\Events\Order\OrderWasPaid;
use App\Models\OrderItem;

class IssueGiftCards
{
    public function handle(OrderWasPaid $event): void
    {
        $event->order
            ->giftCardItems()
            ->get()
            ->reject(fn(OrderItem $item, int $index) => $item->giftCard()->exists())
            ->each(function (OrderItem $item, int $index) use ($event) {
                app(IssueGiftCard::class)->handle($item, [
                    'active' => true,
                    'issuer_id' => auth()->id() ?? $event->order->customer_id,
                ]);
            });
    }
}
