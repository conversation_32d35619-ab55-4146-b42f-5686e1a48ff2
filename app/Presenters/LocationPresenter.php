<?php

namespace App\Presenters;

use App\Models\Location;
use Laracasts\Presenter\Presenter;

class LocationPresenter extends Presenter
{
    public function location(): Location
    {
        return $this->entity;
    }

    public function fullAddress(string $break = '<br>'): string
    {
        $address = addslashes($this->location()->street);

        if ($this->location()->street_2) {
            $address .= ' ' . addslashes($this->location()->street_2);
        }

        $address .= $break . addslashes($this->location()->city) . ', ' . $this->location()->state . ' ' . $this->location()->zip;

        return  $address;
    }

    public function directionsUrl(): string
    {
        return 'https://maps.google.com?daddr=' . urlencode(
            $this->location()->street . '+' . $this->location()->city . '+' . $this->location()->state . '+' . $this->location()->zip
        );
    }
}
