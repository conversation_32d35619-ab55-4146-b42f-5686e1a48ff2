<?php

namespace App\API\V2\Controllers;

use App\Http\Controllers\Controller;
use App\Support\Enums\UserRole;
use Illuminate\Http\JsonResponse;

class CurrentUserController extends Controller
{
    public function __invoke(): JsonResponse
    {
        return response()->json([
            'data' => [
                'id' => auth()->id(),
                'email' => auth()->user()->email,
                'first_name' => auth()->user()->first_name,
                'last_name' => auth()->user()->last_name,
                'role_id' => auth()->user()->role_id,
                'role' => UserRole::get(auth()->user()->role_id),
            ]
        ]);
    }
}
