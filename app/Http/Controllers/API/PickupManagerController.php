<?php

namespace App\Http\Controllers\API;

use App\Actions\Billing\ProcessOrderPayment;
use App\Exceptions\OrderChargeException;
use App\Http\Controllers\Controller;
use App\Models\Card;
use App\Models\Order;
use App\Models\Pickup;
use App\Support\Enums\OrderStatus;
use Illuminate\Http\JsonResponse;

class PickupManagerController extends Controller
{
    public function orders(Pickup $pickup)
    {
        return $pickup->orders()
            ->where('confirmed', true)
            ->where('pickup_id', $pickup->id)
            ->where('canceled', false)
            ->whereIn('status_id', [OrderStatus::packed(), OrderStatus::pickedUp()])
            ->with(['items', 'fees', 'customer', 'discounts'])
            ->orderBy('customer_last_name')
            ->get();
    }

    public function chargeCardOnFile(Order $order): JsonResponse
    {
        if ($order->is_paid) {
            return response()->json(['responseText' => 'This order is already marked as paid'], 409);
        }

        request()->validate([
            'payment_source_id' => ['nullable', 'integer', 'exists:user_cards,id']
        ]);

        $card = null;
        if (request()->has('payment_source_id')) {
            $card = Card::find(request('payment_source_id'));
        }

        $order->load(['customer', 'payments']);

        try {
            app(ProcessOrderPayment::class)->handle($order, $card);
        } catch (OrderChargeException $e) {
            return response()->json(['responseText' => $e->getMessage()], 400);
        }

        return response()->json(['responseText' => 'Order charged to saved card.']);
    }
}
