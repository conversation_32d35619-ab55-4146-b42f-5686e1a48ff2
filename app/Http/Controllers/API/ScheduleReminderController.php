<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Schedule;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ScheduleReminderController extends Controller
{
    public function update(Request $request, int $schedule): JsonResponse
    {
        Schedule::findOrFail($schedule)->update($request->only([
            'reminder_days', 'reminder_hour', 'reminder_minute', 'reminder_meridiem',
            'secondary_reminder_days', 'secondary_reminder_hour', 'secondary_reminder_minute',
            'secondary_reminder_meridiem', 'reminder_enabled', 'secondary_reminder_enabled'
        ]));

        return response()->json('Reminder settings updated');
    }
}
