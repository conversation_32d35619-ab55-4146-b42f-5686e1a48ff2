<?php

namespace App\Http\Controllers\API;

use App\Events\User\CreditIssued;
use App\Events\User\UserUpdated;
use App\Http\Controllers\Controller;
use App\Http\Resources\UserCreditResource;
use App\Models\Event;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class UserCreditController extends Controller
{
    public function index(Request $request, $userId)
    {
        $events = Event::where('model_type', User::class)
           ->with('user')
           ->where('model_id', $userId)
           ->whereIn('event_id', ['initial_credit', 'credit_applied', 'credit_removed'])
           ->orderBy('created_at', 'desc')->orderBy('id', 'desc')->paginate(50);

        return UserCreditResource::collection($events);
    }

    public function store(Request $request, $userId): JsonResponse
    {
        $regex = '/^[0-9,.]*$/';
        $request->validate([
            'amount' => 'required|min:0|regex:' . $regex
        ]);

        $creditAmount = formatCurrencyForDB($request->input('amount'));

        $user = User::findOrFail($userId);
        $this->saveInitialCredit($user);

        DB::transaction(function () use ($user, $creditAmount, $request) {
            $user->applyCredit($creditAmount, $request->filled('reasoning') ? $request->input('reasoning') : 'Credit applied');
        });

        event(new UserUpdated($user));
        event(new CreditIssued($user, [
            'credit_issued' => money($creditAmount),
            'reason' => $request->input('reasoning')
        ]));

        return response()->json([
            'amount' => money($creditAmount)
        ]);
    }

    private function saveInitialCredit($user)
    {
        $existingEvents = $events = Event::where('model_type', User::class)
            ->where('model_id', $user->id)
            ->whereIn('event_id', ['initial_credit', 'credit_applied', 'credit_removed'])
            ->exists();

        if (empty($existingEvents) && $user->credit > 0) {
            $user->recordEvent('initial_credit', [
                'amount' => $user->credit,
            ], 'Initial credit');
        }
    }

    public function destroy(Request $request, $userId): JsonResponse
    {
        $regex = '/^[0-9,.]*$/';
        $request->validate([
            'amount' => 'required|min:0|regex:' . $regex
        ]);

        $creditAmount = formatCurrencyForDB($request->input('amount'));

        $user = User::findOrFail($userId);

        $this->saveInitialCredit($user);

        DB::transaction(function () use ($user, $creditAmount, $request) {
            $user->removeCredit($creditAmount, $request->filled('reasoning') ? $request->input('reasoning') : 'Credit removed');
        });

        return response()->json([
            'amount' => money($creditAmount)
        ]);
    }
}
