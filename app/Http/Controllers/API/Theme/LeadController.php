<?php

namespace App\Http\Controllers\API\Theme;

use Illuminate\Http\RedirectResponse;
use Illuminate\Http\JsonResponse;
use App\Contracts\Geocoder;
use App\Events\User\LeadCreated;
use App\Exceptions\NoGeocodeResultsException;
use App\Http\Controllers\Controller;
use App\Models\Lead;
use GuzzleHttp\Exception\ClientException;
use Illuminate\Http\Request;

class LeadController extends Controller
{
    public function index()
    {
        return Lead::orderBy('created_at')->get();
    }

    public function store(Request $request, Geocoder $geocoder): RedirectResponse
    {
        $request->validate(
            [
                'email' => ['required', 'email'],
                'zip' => ['sometimes', 'required', 'min:4'],
                'location_id' => ['numeric']
            ],
            [
                'zip.required' => 'You zip code is required.',
                'zip.min' => 'You zip code looks invalid.'
            ]
        );

        if (!$request->has('address')) {
            try {
                $address = $geocoder->fromZipcode($request->get('zip'))->getAddressParts();
            } catch (NoGeocodeResultsException | ClientException $e) {
                error('The address you entered could not be found.');
                return redirect()->back();
            }
        } else {
            $address = $request->input('address');
        }

        $lead = Lead::create([
            'email' => $request->get('email'),
            'zip' => $address['postal_code'] ?? null,
            'city' => $address['city'] ?? null,
            'state' => $address['state'] ?? null,
            'lat' => $address['lat'] ?? null,
            'lng' => $address['lng'] ?? null,
            'first_name' => $request->input('first_name'),
            'last_name' => $request->input('last_name'),
            'location_id' => $request->input('location_id', 0),
            'custom_fields' => []
        ]);

        event(new LeadCreated($lead));

        return redirect()->to(setting('lead_capture_url', url('/store')));
    }

    public function update(Request $request, int $leadId): JsonResponse
    {
        $request->validate([
            'email' => ['required', 'email'],
            'zip' => ['sometimes', 'required']
        ]);

        $lead = tap(Lead::findOrFail($leadId))->update($request->only([
            'email', 'zip', 'first_name', 'last_name'
        ]));

        return response()->json($lead);
    }

    public function destroy(int $leadId): JsonResponse
    {
        Lead::findOrFail($leadId)->delete();
        return response()->json(null, 204);
    }
}
