<?php

namespace App\Http\Controllers\API\Theme;

use App\Exceptions\NoGeocodeResultsException;
use App\Http\Controllers\Controller;
use App\Models\Location;
use App\Models\Pickup;
use App\Repositories\DeliveryOptionRepository;
use App\Repositories\LocationRepository;
use App\Services\Geocoding\GeocodedAddress;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;

class PickupsController extends Controller
{
    public function __invoke()
    {
        $validated = request()->validate([
            'type' => ['nullable', 'string'],
            'zip' => ['nullable', 'string', 'min:4', "max:12"],
            'address' => ['nullable', 'string', 'min:4']
        ], [
            'zip.string' => 'The address you entered could not be found.',
            'zip.min' => 'The address you entered could not be found.',
            'zip.max' => 'The address you entered could not be found.',
            'address.min' => 'The address you entered could not be found.',
        ]);

        return match($validated['type'] ?? null) {
            'retail', 'restaurant', 'market' => $this->responseForType($validated),
            default => $this->defaultResponse($validated)
        };
    }

    private function responseForType(array $request): JsonResponse
    {
        try {
            $results = match (true) {
                ! empty($request['zip']) => $this->filteredResultsForTypeAndZip($request['zip'], $request['type']),
                ! empty($request['address']) => $this->filteredResultsForTypeAndAddress($request['address'], $request['type']),
                default => $this->defaultResultsForType($request['type'])
            };
        } catch (NoGeocodeResultsException $exception) {
            return response()->json('Sorry, no ' . $request['type'] . ' locations near your address could be found.', 422);
        }

        return response()->json($results);
    }

    /**
     * @throws NoGeocodeResultsException
     */
    private function filteredResultsForTypeAndZip($zip, $type): array
    {
        return $this->locationTypeResultsToArray(
            app(LocationRepository::class)->getLocationByZipCode($zip, $type)
        );
    }

    public function locationTypeResultsToArray(Collection $locations): array
    {
        return [
            'locations' => $locations,
            'zoom' => (int) setting('map_center_zoom', 8),
            'center' => $this->getMapCenter($locations)
        ];
    }

    private function getMapCenter(Collection $locations): array
    {
        if ($locations->isEmpty()) {
            return config('grazecart.default_map_center');
        }

        $center = setting('map_center');

        if (is_null($center)) {
            return [$locations->first()->lat, $locations->first()->lng];
        }

        $latLng = explode(',', $center);

        // Check for that default value that got accidentally saved to the DB and show first point instead.
        if ($latLng[0] == '40.755989') {
            return [$locations->first()->lat, $locations->first()->lng];
        }

        return $latLng;
    }

    /**
     * @throws NoGeocodeResultsException
     */
    private function filteredResultsForTypeAndAddress($address, $type): array
    {
        return $this->locationTypeResultsToArray(
            app(LocationRepository::class)->getLocationByStreetAddress($address, $type)
        );
    }

    private function defaultResultsForType($type): array
    {
        return $this->locationTypeResultsToArray(
            Location::where('type', $type)
                ->where('visible', true)
                ->orderBy('state')
                ->get()
        );
    }

    private function defaultResponse(array $request): JsonResponse
    {
        try {
            $results = $request['zip'] ?? false
                ? $this->defaultResultsWithGeocoding($request['zip'])
                : $this->defaultResultsWithoutGeocoding();
        } catch (NoGeocodeResultsException $exception) {
            return response()->json('The address you entered could not be found.', 422);
        }

        return response()->json($results);
    }

    /**
     * @throws NoGeocodeResultsException
     */
    private function defaultResultsWithGeocoding(string $zip): array
    {
        $geocoded_address = geocoder()->fromZipcode($zip);

        $delivery_options = app(DeliveryOptionRepository::class)
            ->findByZip($geocoded_address)
            ->findByState($geocoded_address)
            ->getOptions()['delivery']
            ?: [];

        $locations =  $this->buildDefaultResultsQuery()
            ->selectRaw('id, ( 3959 * acos( cos( radians(' . $geocoded_address->lat . ') ) * cos( radians( lat ) ) * cos( radians( lng ) - radians(' . $geocoded_address->lng . ') ) + sin( radians(' . $geocoded_address->lat . ') ) * sin( radians( lat ) ) ) ) AS distance')
            ->reorder()
            ->orderBy('distance')
            ->get()
            ->map(function($pickup): Pickup {
                /** @var Pickup $pickup */
                $pickup->unsetRelation('nextDate');
                $pickup->setAttribute('next_date', $pickup->nextDate?->toOrderWindows()->toDateArray());
                return $pickup;
            });

        return $this->locationResultsToArray($locations, $delivery_options, $geocoded_address);
    }

    private function buildDefaultResultsQuery(): Builder
    {
        return Pickup::query()
            ->select(['id', 'title', 'display_name', 'slug', 'schedule_id', 'status_id', 'street', 'city', 'state', 'zip', 'pickup_times', 'lat', 'lng'])
            ->where('visible', true)
            ->where('fulfillment_type', Pickup::FULFILLMENT_TYPE_PICKUP)
            ->with(['nextDate' => function ($query) {
                return $query->select(['id', 'schedule_id', 'order_start_date', 'order_end_date', 'pickup_date', 'active']);
            }])
            ->orderBy('state')
            ->orderBy('title');
    }

    public function locationResultsToArray(
        Collection $locations,
        array $delivery_options = [],
        GeocodedAddress $geocoded_address = null
    ): array
    {
        return [
            'address' => $geocoded_address ?? [],
            'locations' => $locations,
            'zoom' => setting('map_center_zoom', 10),
            'center' => $this->getMapCenter($locations),
            'closest' => $locations->first(),
            'out_of_range' => $locations->first()?->getDistance() > setting('pickup_results_radius', 400),
            'home_delivery' => $delivery_options,
            'zip' => $geocoded_address?->postalCode,
        ];
    }

    private function defaultResultsWithoutGeocoding(): array
    {
        return $this->locationResultsToArray(
            $this->buildDefaultResultsQuery()
                ->get()
                ->map(function($pickup): Pickup {
                    /** @var Pickup $pickup */
                    $pickup->unsetRelation('nextDate');
                    $pickup->setAttribute('next_date', $pickup->nextDate?->toOrderWindows()->toDateArray());
                    $pickup->append('map_marker_content');
                    return $pickup;
                })
        );
    }
}
