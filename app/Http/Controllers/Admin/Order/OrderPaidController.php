<?php

namespace App\Http\Controllers\Admin\Order;

use App\Http\Controllers\Controller;
use App\Models\Order;

class OrderPaidController extends Controller
{
    public function store(int $orderId)
    {
        $order = Order::findOrFail($orderId);
        $order->markAsPaid();
        flash('The order has been marked as paid.');

        return back();
    }

    public function destroy(int $orderId)
    {
        $order = Order::findOrFail($orderId);
        // $order->markAsUnpaid(); TO DO
        flash('The order has been marked as unpaid.');

        return back();
    }
}
