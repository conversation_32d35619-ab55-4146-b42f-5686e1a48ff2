<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Widget;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class PageWidgetPhotoController extends Controller
{
    public function update(Request $request, int $pageId, int $widgetId): JsonResponse
    {
        $widget = Widget::where('page_id', $pageId)->find($widgetId);
        $settings = $widget->settings;
        $settings->background = $request->get('photo_path');

        $widget->settings = $settings;
        $widget->save();

        return response()->json([
            'responseText' => 'Widget photo has been set.',
        ]);
    }
}
