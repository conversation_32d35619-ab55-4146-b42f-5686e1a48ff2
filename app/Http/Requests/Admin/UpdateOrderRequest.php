<?php

namespace App\Http\Requests\Admin;

use App\Http\Requests\Request;
use App\Models\Order;
use App\Models\Pickup;
use App\Support\Enums\OrderStatus;
use Carbon\Carbon;
use Illuminate\Support\Facades\Validator;

class UpdateOrderRequest extends Request
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'customer_first_name' => [
                'sometimes',
                'required',
            ],
            'customer_last_name' => [
                'sometimes',
                'required',
            ],
            'customer_email' => [
                'sometimes',
                'required',
                'email',
            ],
            'customer_email_alt' => ['nullable', 'email'],
            'recipient_email' => ['nullable', 'email'],
            'recipient_notes' => ['nullable', 'string'],
            'containers' => [
                'min:0',
                'integer',
            ],
            'payment_id' => [
                'sometimes',
                'required',
            ],
            'payment_date' => [
                'date',
            ],
            'due_date' => [
                'date',
            ],
            'paid' => [
                'sometimes',
                'required',
                'integer',
            ],
            'pickup_id' => [
                'sometimes',
                'required',
            ],
            'deadline_date' => [
                'sometimes',
                'required',
                'date',
            ],
            'pickup_date' => [
                'sometimes',
                'required',
                'date',
            ],
            'pack_deadline_at' => [
                'sometimes',
                'required',
                'date',
                'before_or_equal:pickup_date',
            ],
            'order_discount' => [
                'numeric',
            ],
            'delivery_rate' => [
                'sometimes',
                'required',
                'numeric',
                'min:0',
            ],
            'status_id' => [function ($attribute, $value, $fail) {
                /** @var Order|null $order */
                $order = request()->route('order');

                if ($order?->status_id === OrderStatus::preOrder() && (int) $value === OrderStatus::confirmed()) {
                    $fail('Pre-orders cannot be put into "New" status.');
                }
            }]
        ];
    }

    public function sanitize()
    {
        $attributes = $this->all();

        Validator::make($attributes, [
            'pickup_date' => ['date', 'date_format:m/d/Y'],
            'deadline_date' => ['date', 'date_format:m/d/Y'],
            'payment_date' => ['date', 'date_format:m/d/Y'],
            'due_date' => ['date', 'date_format:m/d/Y'],
        ])->validate();

        if (isset($attributes['order_discount'])) {
            $this->merge(['order_discount' => formatCurrencyForDB($this->get('order_discount'))]);
        }

        if (isset($attributes['credit_applied'])) {
            $this->merge(['credit_applied' => formatCurrencyForDB($this->get('credit_applied'))]);
        }

        if (isset($attributes['delivery_rate'])) {
            $this->merge(['delivery_rate' => formatCurrencyForDB($this->get('delivery_rate'))]);
        }

        if (isset($attributes['pickup_id'])) {
            $this->merge(['schedule_id' => Pickup::findOrFail($this->get('pickup_id'))->schedule_id]);
        }

        if (isset($attributes['pickup_date'])) {
            $this->merge(['pickup_date' => Carbon::createFromFormat('m/d/Y', $this->get('pickup_date'))->format('Y-m-d')]);
        }

        if (isset($attributes['deadline_date'])) {
            $this->merge(['deadline_date' => Carbon::createFromFormat('m/d/Y', $this->get('deadline_date'))->format('Y-m-d')]);
        }

        if (isset($attributes['payment_date'])) {
            $this->merge(['payment_date' => Carbon::createFromFormat('m/d/Y', $this->get('payment_date'))->format('Y-m-d')]);
        }

        if (isset($attributes['due_date'])) {
            $this->merge(['due_date' => Carbon::createFromFormat('m/d/Y', $this->get('due_date'))->format('Y-m-d')]);
        }

        return $attributes;
    }
}
