<?php

namespace App\Services\FilterService\Filters;

use Illuminate\Http\Request;

class Payment extends Filter
{
    public static string $query_param = 'payment_id';

    protected string $label = 'Payment:';

    public function setValue(Request $request): void
    {
        $id = $request->get(static::$query_param);

        if (is_null($id)) return;

        $this->value = \App\Models\Payment::whereIn('id', \Arr::wrap($id))
            ->pluck('title')
            ->implode(', ');
    }
}