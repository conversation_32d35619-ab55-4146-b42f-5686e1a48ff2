<?php

namespace App\Livewire\Theme;

use App\Services\StoreService;
use Livewire\Component;

class DeliveryDate extends Component
{
    use FetchesCart;

    public string $date;

    public bool $is_delivery = true;

    protected $listeners = [
        'cartDeliveryMethodUpdated' => 'refreshDate',
    ];

    public function mount()
    {
       $this->refreshDate();
    }

    public function refreshDate()
    {
        $delivery_method = app(StoreService::class)->deliveryMethod();

        $this->is_delivery = $delivery_method?->isDeliveryZone() ?? true;

        $this->date = $delivery_method?->activeOrderWindow()
            ?->deliveryDatetime()
            ->format('M jS')
            ?? 'TBD';
    }

    public function render()
    {
        return <<<'blade'
            <p class="tw-m-0 tw-text-xs tw-text-white">{{ $is_delivery ? 'Delivery' : 'Pickup' }} by
                <span class="tw-font-semibold">
                   {{ $date }}
                </span>
            </p>
        blade;
    }
}
