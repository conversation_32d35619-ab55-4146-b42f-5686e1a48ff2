<?php

namespace App\Livewire\Theme;

use App\Actions\Order\SyncItemToSubscription;
use App\Events\Order\OrderUpdated;
use App\Events\Subscription\SubscriptionUpdated;
use App\Models\Order;
use App\Models\Product;
use App\Services\SubscriptionSettingsService;
use Livewire\Component;

class OrderItems extends Component
{
    use FetchesOrder;

    public ?Order $order;

    protected $listeners = [
        'orderUpdated' => 'refreshOrder',
    ];

    public function render()
    {
        return view('theme::livewire.order-items', [
            'available_promotion_products' => $this->order->isFromBlueprint()
                ? Product::query()
                    ->with(['price' => fn($q) => $q->where('group_id', $this->order->getPricingGroup())])
                    ->findMany(app(SubscriptionSettingsService::class)->productIncentiveIds())
                : collect(),
        ]);
    }

    public function incrementItemQuantity(int $id)
    {
        if ($this->order->deadlineHasPassed()) {
            $this->dispatch('openModal', title: 'Unable to update item', message: 'The order modification deadline has passed.');
            return;
        }

        $item = $this->order->items()->find($id);

        if (is_null($item)) return;

        try {
            $updated_order_item = $this->order->updateItemQuantity($item, $item->qty + 1);
        } catch (\Exception $exception) {
            $this->dispatch('openModal', title: 'Unable to update quantity', message: $exception->getMessage());
            return;
        }

        if ( ! is_null($this->order->blueprint) && $updated_order_item->type !== 'addon') {
            app(SyncItemToSubscription::class)->handle($updated_order_item);
        }

        event(new OrderUpdated($this->order));

        if ($this->order->isFromBlueprint()) {
            event(new SubscriptionUpdated($this->order->blueprint));
        }

        $this->dispatch('orderUpdated');
    }

    public function decrementItemQuantity(int $id)
    {
        if ($this->order->deadlineHasPassed()) {
            $this->dispatch('openModal', title: 'Unable to update item', message: 'The order modification deadline has passed.');
            return;
        }

        $item = $this->order->items()->find($id);

        if (is_null($item)) return;

        if ($item->qty - 1 <= 0) {
            $this->removeItem($id);
            return;
        }

        try {
            $updated_order_item = $this->order->updateItemQuantity($item, $item->qty - 1);
        } catch (\Exception $exception) {
            $this->dispatch('openModal', title: 'Unable to update quantity', message: $exception->getMessage());
            return;
        }

        if ( ! is_null($this->order->blueprint) && $updated_order_item->type !== 'addon') {
            app(SyncItemToSubscription::class)->handle($updated_order_item);
        }

        event(new OrderUpdated($this->order));
        if ($this->order->isFromBlueprint()) {
            event(new SubscriptionUpdated($this->order->blueprint));
        }

        $this->dispatch('orderUpdated');
    }

    public function removeItem(int $id)
    {
        if ($this->order->deadlineHasPassed()) {
            $this->dispatch('openModal', title: 'Unable to remove item', message: 'The order modification deadline has passed.');
            return;
        }

        $item = $this->order->items()->find($id);

        if (is_null($item)) return;

        try {
            $this->order->updateItemQuantity($item, 0);
        } catch (\Exception $exception) {
            $this->dispatch('openModal', title: 'Unable to remove item', message: $exception->getMessage());
            return;
        }

        if ( ! is_null($this->order->blueprint)) {
            $this->order->blueprint
                ->items()
                ->where([
                    'product_id' => $item->product_id,
                    'type' => $item->type === 'standard' ? 'recurring' : $item->type
                ])
                ->delete();
        }

        event(new OrderUpdated($this->order));

        if ($this->order->isFromBlueprint()) {
            event(new SubscriptionUpdated($this->order->blueprint));
        }

        $this->dispatch('orderUpdated');
    }

    public function refreshOrder()
    {
        $this->order = !is_null($this->order) ? $this->findOrder($this->order->id) : $this->fetchCustomerOrder();
    }

    public function updatePromoProduct(int $product_id): void
    {
        if (app(SubscriptionSettingsService::class)->productIncentiveIds()->doesntContain($product_id)) {
            return;
        }

        if ($this->order->deadlineHasPassed()) {
            $this->dispatch('openModal', title: 'Unable to update product', message: 'The order modification deadline has passed.');
            return;
        }

        $current_promo_item = $this->order->findPromotionalItem();

        if ($current_promo_item?->id === $product_id) return;

        $product = Product::query()
            ->with(['price' => fn($q) => $q->where('group_id', $this->order->getPricingGroup())])
            ->find($product_id);

        if (is_null($product)) {
            $this->dispatch('openModal', title: 'Unable to update product', message: 'The product is no longer available.');
            return;
        }

        try {
            $updated_promo_item = $this->order->addItem(product: $product, qty: 1, type: 'promo');;
        } catch (\Exception $exception) {
            $this->dispatch('openModal', title: 'Unable to update promo product', message: $exception->getMessage());
            return;
        }

        $current_promo_item?->delete();

        app(SyncItemToSubscription::class)->handle($updated_promo_item);

        $this->order->updateTotals();

        event(new OrderUpdated($this->order));
        event(new SubscriptionUpdated($this->order->blueprint));

        $this->dispatch('subscriptionUpdated');
    }
}
