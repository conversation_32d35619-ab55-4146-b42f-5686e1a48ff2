<?php

namespace App\Livewire\Theme;

use App\Contracts\Cartable;
use App\Exceptions\NoGeocodeResultsException;
use App\Models\Address;
use App\Models\Cart;
use App\Models\Order;
use App\Models\Pickup;
use App\Models\RecurringOrder;
use App\Models\User;
use App\Services\DeliveryMethodService;
use App\Services\Geocoding\GeocodedAddress;
use App\Services\SettingsService;
use App\Services\StoreService;
use App\Support\FetchesGeocodedAddress;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Once;
use Livewire\Component;

class DeliveryMethodToggle extends Component
{
    use FetchesCart;
    use FetchesGeocodedAddress;

    public ?Cartable $cart = null;

    public ?Order $order = null;

    public ?RecurringOrder $subscription = null;

    public ?Pickup $current_delivery_method = null;

    public ?int $selected_delivery_method_id = null;

    public string $postal_code = '';

    public bool $open = false;

    public bool $searching = false;

    public ?string $delivery_method_type = 'delivery';

    public ?bool $delivery_is_available = null;

    public ?string $geocoded_city = null;

    public ?bool $has_addresses = false;

    /** @var Collection<Address>|null */
    public ?Collection $addresses = null;

    public bool $selecting_address = false;

    public ?int $selected_address_id = null;

    public $listeners = [
        'addressAdded',
        'cartDeliveryMethodUpdated',
    ];

    public function mount(): void
    {
        $this->initialize();
    }

    private function initialize(): void
    {
        $delivery_method = app(StoreService::class)->deliveryMethod();

        if (empty($this->postal_code) && ! is_null($this->order)) {
            $this->setAddressAttributes($delivery_method, [
                'address_id' => null,
                'city' => $this->order->shipping_city,
                'state' => $this->order->shipping_state,
                'postal_code' => $this->order->shipping_zip,
            ]);
        }

        if (empty($this->postal_code) && ! is_null($this->subscription)) {
            $this->setAddressAttributes($delivery_method, $this->subscription->getShippingInfo());
        }

        if (empty($this->postal_code) &&! is_null($this->cart)) {
            $shipping = $this->cart->getShippingInfo();

            if (
                ! is_null(auth()->user())
                && (is_null($delivery_method) || empty($shipping['street']))
            ) {
                $shipping = auth()->user()->defaultShippingAttributes();
                $delivery_method = $this->deliveryMethodForAddress(new GeocodedAddress(
                    0.0,
                    0.0,
                    $shipping['city'],
                    $shipping['state'],
                    $shipping['postal_code'],
                    $shipping['country'],
                    1,
                ));

                if (! is_null($delivery_method)) {
                    $this->cart->updateCartLocation($delivery_method);
                    $this->cart->setShippingInfo($shipping);
                    $this->dispatch('cartUpdated');
                }
            }

            $this->setAddressAttributes($delivery_method, $shipping);
        }

        if (empty($this->postal_code)) {
            $this->geocoded_city = Cookie::get('shopping_city') ?? '';
            $this->postal_code = Cookie::get('shopping_postal_code') ?? '';

            if ($delivery_method_id = Cookie::get('shopping_delivery_method_id') ?? false) {
                $delivery_method = Pickup::find($delivery_method_id);
            }
        }

        if (! is_null($delivery_method)) {
            $this->setDeliveryMethod($delivery_method);
        }
    }

    private function setAddressAttributes(?Pickup $delivery_method, array $shipping = []): void
    {
        if ($delivery_method?->isPickup()) {
            $this->selected_address_id = null;
            $this->geocoded_city = $delivery_method->city;
            $this->postal_code = $delivery_method->zip;
        }

        if ($delivery_method?->isDeliveryZone()) {
            $this->selected_address_id = $shipping['address_id'] ?? null;
            $this->geocoded_city = $shipping['city'] ?? '';
            $this->postal_code = $shipping['postal_code'] ?? $shipping['zip'] ??  '';
        }
    }

    public function deliveryMethodForAddress(GeocodedAddress $address, array $product_ids = []): ?Pickup
    {
        return app(DeliveryMethodService::class)
            ->deliveryZones()
            ->hasProducts($product_ids)
            ->find($address)
            ->first();
    }

    private function setDeliveryMethod(Pickup $delivery_method): void
    {
        $this->current_delivery_method = $delivery_method;
        $this->selected_delivery_method_id = $delivery_method->id;

        if ($delivery_method->isDeliveryZone()) {
            $this->delivery_is_available = true;
            $this->delivery_method_type = 'delivery';
            $this->initializeAddresses();
        }

        if ($delivery_method->isPickup()) {
            $this->delivery_method_type = 'pickup';
            $this->geocoded_city = $delivery_method->city;
            $this->postal_code = $delivery_method->zip;
        }
    }

    private function initializeAddresses(): void
    {
        $this->has_addresses = $this->fetchAddresses()->isNotEmpty();
    }

    public function fetchAddresses(): Collection
    {
        return auth()->user()?->addresses()->get()
            ?? collect();
    }

    public function render()
    {
        return view('theme::livewire.delivery-method-toggle');
    }

    public function setTypeAsDelivery(): void
    {
        $this->delivery_method_type = 'delivery';

        if (auth()->user()) {
            $this->initializeAddresses();
            $this->changeAddress();
        }
    }

    public function changeAddress(): void
    {
        $this->selecting_address = true;
        $this->addresses = $this->fetchAddresses();
        $this->has_addresses = $this->addresses->isNotEmpty();
    }

    public function setTypeAsPickup(): void
    {
        $this->delivery_is_available = null;
        $this->delivery_method_type = 'pickup';

        $settings = app(SettingsService::class);

        $this->geocoded_city = $settings->farmCity();
        $this->postal_code = trim($settings->farmPostalCode());

        $delivery_method = $this->pickupMethod(new GeocodedAddress(
            40.9378297,
            -85.319501,
            $settings->farmCity(),
            $settings->farmState(),
            $this->postal_code,
            $settings->farmCountry(),
            1,
        ));

        $this->setDeliveryMethod($delivery_method);

        Cookie::queue(Cookie::make('shopping_city', $this->geocoded_city, 259200));
        Cookie::queue(Cookie::make('shopping_postal_code', $this->postal_code, 259200));
        Cookie::queue(Cookie::make('shopping_delivery_method_id', (string) $delivery_method->id, 259200));

        $cart = $this->fetchShopperCart(should_stub: false);
        if (!is_null($cart)) {
            $this->cart->setShippingInfo([
                'address_id' => null,
                'street' => '',
                'street_2' => '',
                'city' => '',
                'state' => '',
                'postal_code' => '',
                'country' => $settings->farmCountry(),
            ]);
            $cart->updateCartLocation($delivery_method);
            $this->dispatch('cartUpdated');
        }

        $this->dispatch('cartDeliveryMethodUpdated');
        auth()->user()?->update(['pickup_point' => $delivery_method->id]);

        Once::flush();
    }

    /**
     * Assumes there will always be at least one farm pickup
     */
    public function pickupMethod(GeocodedAddress $address): Pickup
    {
        return app(DeliveryMethodService::class)
            ->pickupLocations()
            ->find($address)
            ->first();
    }

    public function searchPostalCode(): void
    {
        if ($this->searching || empty($this->postal_code)) {
            return;
        }

        $this->searching = true;
        $this->geocoded_city = null;
        $this->delivery_is_available = null;
        $this->postal_code = trim($this->postal_code);

        try {
            $geocoded_address = $this->fetchGeocodedAddressFromPostalCode($this->postal_code);
        } catch (NoGeocodeResultsException $e) {
            $this->delivery_is_available = false;
            $this->searching = false;
            return;
        }

        /** @var Cart|null $cart */
        $cart = $this->fetchShopperCart(should_stub: false);
        $this->cart = $cart;

        $product_ids = $this->cart
            ?->cartProductIds()
            ->toArray()
            ?? [];

        /** @var Pickup|null $delivery_method */
        $delivery_method = $this->deliveryMethodForAddress($geocoded_address, $product_ids);

        $this->delivery_is_available = false;

        if (! is_null($delivery_method)) {
            $this->confirmDelivery($delivery_method, $geocoded_address);

            Cookie::queue(Cookie::make('shopping_city', $this->geocoded_city, 259200));
            Cookie::queue(Cookie::make('shopping_postal_code', $this->postal_code, 259200));
            Cookie::queue(Cookie::make('shopping_delivery_method_id', (string) $delivery_method->id, 259200));

            /** @var User|null $user */
            $user = auth()->user();
            $user?->update(['pickup_point' => $delivery_method->id]);

            if ($user?->addresses()->doesntExist() ?? false) {
                $user->update(['zip' => $this->postal_code]);
            }
        }

        $this->dispatch('cartDeliveryMethodUpdated');
        Once::flush();

        $this->searching = false;
    }

    private function confirmDelivery(Pickup $delivery_method, GeocodedAddress $geocoded_address): void
    {
        $this->delivery_is_available = true;
        $this->geocoded_city = $geocoded_address->city;
        $this->selected_delivery_method_id = $delivery_method->id;

        $this->setDeliveryMethod($delivery_method);
        // TODO: Determine if we should use the already loaded cart or if I should refresh here
//        $this->cart?->updateCartLocation($delivery_method);
//        $this->fetchShopperCart(should_stub: false)?->updateCartLocation($delivery_method);
    }

    public function addressAdded(): void
    {
        $this->changeAddress();
        $this->selected_address_id = $this->addresses->last()?->id;
    }

    public function cartDeliveryMethodUpdated(): void
    {
        /** @var Cart|null $cart */
        $cart = $this->fetchShopperCart(should_stub: false);
        $this->cart = $cart;
        $this->setAddressAttributes($this->cart?->cartLocation(), $this->cart?->getShippingInfo() ?? []);
    }

    public function confirmAddressChange(): void
    {
        if (is_null($this->selected_address_id)) {
            return;
        }

        $current_address = $this->getCurrentAddressProperty();

        if (! is_null($current_address)) {
            $delivery_method = $this->deliveryMethodForAddress($current_address->toGeocodedAddress());

            $shipping = [
                'address_id' => $current_address->id,
                'street' => $current_address->street,
                'street_2' => $current_address->location->street_2,
                'city' => $current_address->city,
                'state' => $current_address->state,
                'zip' => $current_address->postal_code,
                'country' => $current_address->country,
            ];

            $cart = $this->fetchShopperCart();
            if (! is_null($cart)) {
                $cart->setShippingInfo($shipping);
                $cart->updateCartLocation($delivery_method);
                $this->dispatch('addressUpdated');
                $this->dispatch('cartUpdated');
            }

            $this->setAddressAttributes($delivery_method, $shipping);
            $this->setDeliveryMethod($delivery_method);

            auth()->user()?->update(['pickup_point' => $delivery_method->id]);
        }

        $this->dispatch('cartDeliveryMethodUpdated');
        Once::flush();

        $this->close();
    }

    public function getCurrentAddressProperty(): ?Address
    {
        return auth()->user()
            ?->addresses()
            ->where('id', $this->selected_address_id)
            ->first();
    }

    public function close(): void
    {
        $this->reset(['open', 'searching', 'addresses', 'selecting_address']);
    }
}
