<?php

namespace App\Livewire\Admin;

use App\Models\Price;
use App\Models\Product;
use Livewire\Attributes\On;
use Livewire\Component;

class ProductPrices extends Component
{
    use SendsAdminNotifications;

    public Product $product;

    public array $prices;

    public function mount(Product $product)
    {
        if ($this->product->prices()->where(['quantity' => 1])->doesntExist()) {
            $this->product->prices()->create([
                'quantity' => 1,
                'unit_price' => $this->product->unit_price ?? 0,
                'sale_unit_price' => $this->product->sale_unit_price ?? 0,
                'auto_percentage' => null,
            ]);
        }

        $this->setPrices();
    }

    private function setPrices(): void
    {
        $this->prices = $this->product->prices()
            ->orderBy('quantity')
            ->get()
            ->toArray();
    }

    public function render()
    {
        return view('livewire.product-prices');
    }

    #[On('price-tier-added'), On('price-tier-updated')]
    public function priceTierAdded(int $product_id, int $quantity)
    {
        $price = Price::query()
            ->where(['product_id' => $this->product->id, 'quantity' => $quantity])
            ->first()
            ?->toArray();

        if (is_null($price)) return;

        $this->setPrices();
    }

    #[On('price-tier-removed')]
    public function remove(int $quantity)
    {
        Price::query()
            ->where(['product_id' => $this->product->id, 'quantity' => $quantity])
            ->delete();

        $this->sendAdminNotification([
            'level' => 'success',
            'title' => 'Success!',
            'message' => 'The price tier has been removed.',
        ]);

        $this->setPrices();
    }
}
