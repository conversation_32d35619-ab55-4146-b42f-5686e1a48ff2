<?php

namespace App\Livewire\Admin\Pages\Widgets;

trait HasIdentifiers
{
    public string $name;
    public string $html_id;

    protected function initializeIdentifiers(array $settings)
    {
        $this->name = $settings['name'] ?? '';
        $this->html_id = $settings['html_id'] ?? str($this->name)->slug();
    }

    protected function identiferRules()
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'html_id' => ['nullable', 'alpha_dash', 'max:255'],
        ];
    }
}
