tinymce.PluginManager.add("textpattern",function(e){function t(){return u&&(l.sort(function(e,t){return e.start.length>t.start.length?-1:e.start.length<t.start.length?1:0}),u=!1),l}function n(e){for(var n=t(),r=0;r<n.length;r++)if(0===e.indexOf(n[r].start)&&(!n[r].end||e.lastIndexOf(n[r].end)==e.length-n[r].end.length))return n[r]}function r(e,n,r){var i,o,a;for(i=t(),a=0;a<i.length;a++)if(o=i[a],o.end&&e.substr(n-o.end.length-r,o.end.length)==o.end)return o}function i(t){function i(){l=l.splitText(c),l.splitText(u-c-h),l.deleteData(0,p.start.length),l.deleteData(l.data.length-p.end.length,p.end.length)}var o,a,s,l,u,c,d,f,p,h,m;if(o=e.selection,a=e.dom,o.isCollapsed()&&(s=o.getRng(!0),l=s.startContainer,u=s.startOffset,d=l.data,h=t?1:0,3==l.nodeType&&(p=r(d,u,h),p&&(c=Math.max(0,u-h),c=d.lastIndexOf(p.start,c-p.end.length-1),c!==-1&&(f=a.createRng(),f.setStart(l,c),f.setEnd(l,u-h),p=n(f.toString()),p&&p.end&&!(l.data.length<=p.start.length+p.end.length))))))return m=e.formatter.get(p.format),m&&m[0].inline?(i(),e.formatter.apply(p.format,{},l),l):void 0}function o(){var t,r,i,o,a,s,l,u,c,d,f;if(t=e.selection,r=e.dom,t.isCollapsed()&&(l=r.getParent(t.getStart(),"p"))){for(c=new tinymce.dom.TreeWalker(l,l);a=c.next();)if(3==a.nodeType){o=a;break}if(o){if(u=n(o.data),!u)return;if(d=t.getRng(!0),i=d.startContainer,f=d.startOffset,o==i&&(f=Math.max(0,f-u.start.length)),tinymce.trim(o.data).length==u.start.length)return;u.format&&(s=e.formatter.get(u.format),s&&s[0].block&&(o.deleteData(0,u.start.length),e.formatter.apply(u.format,{},o),d.setStart(i,f),d.collapse(!0),t.setRng(d))),u.cmd&&e.undoManager.transact(function(){o.deleteData(0,u.start.length),e.execCommand(u.cmd)})}}}function a(){var t,n;n=i(),n&&(t=e.dom.createRng(),t.setStart(n,n.data.length),t.setEnd(n,n.data.length),e.selection.setRng(t)),o()}function s(){var t,n,r,o,a;t=i(!0),t&&(a=e.dom,n=t.data.slice(-1),/[\u00a0 ]/.test(n)&&(t.deleteData(t.data.length-1,1),r=a.doc.createTextNode(n),t.nextSibling?a.insertAfter(r,t.nextSibling):t.parentNode.appendChild(r),o=a.createRng(),o.setStart(r,1),o.setEnd(r,1),e.selection.setRng(o)))}var l,u=!0;l=e.settings.textpattern_patterns||[{start:"*",end:"*",format:"italic"},{start:"**",end:"**",format:"bold"},{start:"#",format:"h1"},{start:"##",format:"h2"},{start:"###",format:"h3"},{start:"####",format:"h4"},{start:"#####",format:"h5"},{start:"######",format:"h6"},{start:"1. ",cmd:"InsertOrderedList"},{start:"* ",cmd:"InsertUnorderedList"},{start:"- ",cmd:"InsertUnorderedList"}],e.on("keydown",function(e){13!=e.keyCode||tinymce.util.VK.modifierPressed(e)||a()},!0),e.on("keyup",function(e){32!=e.keyCode||tinymce.util.VK.modifierPressed(e)||s()}),this.getPatterns=t,this.setPatterns=function(e){l=e,u=!0}});