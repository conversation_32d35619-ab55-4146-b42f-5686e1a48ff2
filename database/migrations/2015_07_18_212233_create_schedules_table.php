<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('schedules', function (Blueprint $table) {
            $table->increments('id');
            $table->string('title');
            $table->string('slug')->unique();
            $table->boolean('active')->default(true);
            $table->text('notes');

            $table->boolean('reminder_enabled')->default(false);
            $table->tinyInteger('reminder_days')->unsigned();
            $table->tinyInteger('reminder_hour')->unsigned();
            $table->tinyInteger('reminder_minute')->unsigned();
            $table->string('reminder_meridiem', 2)->default('AM');

            $table->boolean('secondary_reminder_enabled')->default(false);
            $table->tinyInteger('secondary_reminder_days')->unsigned();
            $table->tinyInteger('secondary_reminder_hour')->unsigned();
            $table->tinyInteger('secondary_reminder_minute')->unsigned();
            $table->string('secondary_reminder_meridiem', 2)->default('AM');

            $table->integer('template_id');
            $table->integer('secondary_template_id');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::drop('schedules');
    }
};
