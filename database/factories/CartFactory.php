<?php

namespace Database\Factories;

use App\Models\Cart;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class CartFactory extends Factory
{
    public function definition(): array
    {
        $user = User::factory()->create();

        return [
            'shopper_type' => User::class,
            'shopper_id' => $user->id,
            'type' => 'default',  // 'default', 'product', 'shared'
            'extra_attributes' => Cart::initialAttributesForShopper($user)
        ];
    }
}
