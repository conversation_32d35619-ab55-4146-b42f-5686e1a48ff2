<?php

namespace Tests\Unit\Models;

use App\Models\Media;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class MediaTest extends TenantTestCase
{
    #[Test]
    public function it_transforms_an_s3_Url_to_Cloudfront_Url(): void
    {
        $path = 'https://s3.amazonaws.com/bucket-name/path/to/file.jpg';
        $expected = 'https://mycloudfront.url/path/to/file.jpg';

        config(['services.cloudfront.url' => 'https://mycloudfront.url']);
        config(['filesystems.disks.s3.bucket' => 'bucket-name']);

        $result = Media::s3ToCloudfront($path);

        $this->assertEquals($expected, $result);
    }

    #[Test]
    public function it_doesnt_transform_an_s3_Url_when_the_path_is_null(): void
    {
        $path = null;

        config(['services.cloudfront.url' => 'https://mycloudfront.url']);

        $result = Media::s3ToCloudfront($path);

        $this->assertNull($result);
    }

    #[Test]
    public function it_doesnt_transform_an_s3_Url_when_the_cloudfront_url_is_null(): void
    {
        $path = 'https://s3.amazonaws.com/bucket-name/path/to/file.jpg';

        config(['services.cloudfront.url' => null]);

        $result = Media::s3ToCloudfront($path);

        $this->assertEquals($path, $result);
    }

    #[Test]
    public function it_doesnt_transform_an_s3_Url_when_the_bucket_name_doesnt_match(): void
    {
        $path = 'https://s3.amazonaws.com/another-bucket/path/to/file.jpg';

        config(['services.cloudfront.url' => 'https://mycloudfront.url']);
        config(['filesystems.disks.s3.bucket' => 'bucket-name']);

        $result = Media::s3ToCloudfront($path);

        $this->assertEquals($path, $result);
    }
}