<?php

namespace Tests\Unit\Jobs;

use App\Jobs\TagTopCustomers;
use App\Models\Order;
use App\Models\Tag;
use App\Models\User;
use App\Support\Enums\Channel;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class TagTopCustomersTest extends TenantTestCase
{
    #[Test]
    public function it_tags_top_customers_correctly(): void
    {
        $order_one = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'total' => 3000, 'type_id' => Channel::SHIPPING, 'confirmed_date' => today()->subDay()]);
        $order_two = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'total' => 10000, 'type_id' => Channel::SHIPPING, 'confirmed_date' => today()->subDay()]);
        $order_three = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'total' => 500, 'type_id' => Channel::SHIPPING, 'confirmed_date' => today()->subDay()]);
        $order_four = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'total' => 1000, 'type_id' => Channel::SHIPPING, 'confirmed_date' => today()->subDay()]);
        $order_five = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'total' => 150, 'type_id' => Channel::SHIPPING, 'confirmed_date' => today()->subDay()]);

        (new TagTopCustomers(3))->handle();

        $tag = Tag::where('slug', 'top-3')->first();

        $this->assertDatabaseHas(Tag::class, [
            'title' => 'Top 3',
            'slug' => $tag->slug,
            'type' => Tag::type('user')
        ]);

        $this->assertDatabaseHas('tag_user', [
            'user_id' => $order_one->customer_id,
            'tag_id' => $tag->id
        ]);

        $this->assertDatabaseHas('tag_user', [
            'user_id' => $order_two->customer_id,
            'tag_id' => $tag->id
        ]);

        $this->assertDatabaseMissing('tag_user', [
            'user_id' => $order_three->customer_id,
            'tag_id' => $tag->id
        ]);

        $this->assertDatabaseHas('tag_user', [
            'user_id' => $order_four->customer_id,
            'tag_id' => $tag->id
        ]);

        $this->assertDatabaseMissing('tag_user', [
            'user_id' => $order_five->customer_id,
            'tag_id' => $tag->id
        ]);
    }

    #[Test]
    public function it_does_not_include_orders_from_more_than_a_year_ago(): void
    {
        $order_one = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'total' => 3000, 'type_id' => Channel::SHIPPING, 'confirmed_date' => today()->subDay()]);
        $order_two = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'total' => 10000, 'type_id' => Channel::SHIPPING, 'confirmed_date' => today()->subYear()->subDay()]);
        $order_three = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'total' => 500, 'type_id' => Channel::SHIPPING, 'confirmed_date' => today()->subDay()]);
        $order_four = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'total' => 1000, 'type_id' => Channel::SHIPPING, 'confirmed_date' => today()->subDay()]);
        $order_five = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'total' => 150, 'type_id' => Channel::SHIPPING, 'confirmed_date' => today()->subDay()]);

        (new TagTopCustomers(3))->handle();

        $tag = Tag::where('slug', 'top-3')->first();

        $this->assertDatabaseHas(Tag::class, [
            'title' => 'Top 3',
            'slug' => $tag->slug,
            'type' => Tag::type('user')
        ]);

        $this->assertDatabaseHas('tag_user', [
            'user_id' => $order_one->customer_id,
            'tag_id' => $tag->id
        ]);

        $this->assertDatabaseMissing('tag_user', [
            'user_id' => $order_two->customer_id,
            'tag_id' => $tag->id
        ]);

        $this->assertDatabaseHas('tag_user', [
            'user_id' => $order_three->customer_id,
            'tag_id' => $tag->id
        ]);

        $this->assertDatabaseHas('tag_user', [
            'user_id' => $order_four->customer_id,
            'tag_id' => $tag->id
        ]);

        $this->assertDatabaseMissing('tag_user', [
            'user_id' => $order_five->customer_id,
            'tag_id' => $tag->id
        ]);
    }

    #[Test]
    public function it_does_not_include_orders_from_certain_sales_channels(): void
    {
        $order_one = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'total' => 3000, 'type_id' => Channel::SHIPPING, 'confirmed_date' => today()->subDay()]);
        $order_two = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'total' => 10000, 'type_id' => Channel::WHOLESALE, 'confirmed_date' => today()->subDay()]);
        $order_three = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'total' => 500, 'type_id' => Channel::SHIPPING, 'confirmed_date' => today()->subDay()]);
        $order_four = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'total' => 1000, 'type_id' => Channel::SHIPPING, 'confirmed_date' => today()->subDay()]);
        $order_five = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'total' => 150, 'type_id' => Channel::SHIPPING, 'confirmed_date' => today()->subDay()]);

        (new TagTopCustomers(3))->handle();

        $tag = Tag::where('slug', 'top-3')->first();

        $this->assertDatabaseHas(Tag::class, [
            'title' => 'Top 3',
            'slug' => $tag->slug,
            'type' => Tag::type('user')
        ]);

        $this->assertDatabaseHas('tag_user', [
            'user_id' => $order_one->customer_id,
            'tag_id' => $tag->id
        ]);

        $this->assertDatabaseMissing('tag_user', [
            'user_id' => $order_two->customer_id,
            'tag_id' => $tag->id
        ]);

        $this->assertDatabaseHas('tag_user', [
            'user_id' => $order_three->customer_id,
            'tag_id' => $tag->id
        ]);

        $this->assertDatabaseHas('tag_user', [
            'user_id' => $order_four->customer_id,
            'tag_id' => $tag->id
        ]);

        $this->assertDatabaseMissing('tag_user', [
            'user_id' => $order_five->customer_id,
            'tag_id' => $tag->id
        ]);
    }

    #[Test]
    public function it_does_not_include_orders_from_deleted_customer(): void
    {
        $deleted_customer = User::deletedCustomer();

        $order_one = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'total' => 3000, 'type_id' => Channel::SHIPPING, 'confirmed_date' => today()->subDay()]);
        $order_two = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'total' => 10000, 'type_id' => Channel::SHIPPING, 'confirmed_date' => today()->subDay(), 'customer_id' => $deleted_customer->id]);
        $order_three = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'total' => 500, 'type_id' => Channel::SHIPPING, 'confirmed_date' => today()->subDay()]);
        $order_four = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'total' => 1000, 'type_id' => Channel::SHIPPING, 'confirmed_date' => today()->subDay()]);
        $order_five = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'total' => 150, 'type_id' => Channel::SHIPPING, 'confirmed_date' => today()->subDay()]);

        (new TagTopCustomers(3))->handle();

        $tag = Tag::where('slug', 'top-3')->first();

        $this->assertDatabaseHas(Tag::class, [
            'title' => 'Top 3',
            'slug' => $tag->slug,
            'type' => Tag::type('user')
        ]);

        $this->assertDatabaseHas('tag_user', [
            'user_id' => $order_one->customer_id,
            'tag_id' => $tag->id
        ]);

        $this->assertDatabaseMissing('tag_user', [
            'user_id' => $order_two->customer_id,
            'tag_id' => $tag->id
        ]);

        $this->assertDatabaseHas('tag_user', [
            'user_id' => $order_three->customer_id,
            'tag_id' => $tag->id
        ]);

        $this->assertDatabaseHas('tag_user', [
            'user_id' => $order_four->customer_id,
            'tag_id' => $tag->id
        ]);

        $this->assertDatabaseMissing('tag_user', [
            'user_id' => $order_five->customer_id,
            'tag_id' => $tag->id
        ]);
    }

    #[Test]
    public function it_removes_existing_top_tags(): void
    {
        $tag = Tag::firstOrCreate([
            'title' => "Top 3",
            'type' => Tag::type('user'),
        ]);

        $user_one = User::factory()->create();
        $user_one->tags()->attach($tag->id);

        $user_two = User::factory()->create();
        $user_two->tags()->attach($tag->id);

        (new TagTopCustomers(3))->handle();

        $this->assertDatabaseHas(Tag::class, [
            'title' => 'Top 3',
            'slug' => $tag->slug,
            'type' => Tag::type('user')
        ]);

        $this->assertDatabaseMissing('tag_user', [
            'user_id' => $user_one->id,
            'tag_id' => $tag->id
        ]);

        $this->assertDatabaseMissing('tag_user', [
            'user_id' => $user_two->id,
            'tag_id' => $tag->id
        ]);
    }
}
