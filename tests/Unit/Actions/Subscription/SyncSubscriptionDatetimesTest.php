<?php

namespace Tests\Unit\Actions\Subscription;

use App\Actions\Subscription\SyncSubscriptionDatetimes;
use App\Models\Date;
use App\Models\Pickup;
use App\Models\RecurringOrder;
use App\Models\Schedule;
use App\Models\Setting;
use Illuminate\Support\Carbon;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class SyncSubscriptionDatetimesTest extends TenantTestCase
{
    #[Test]
    public function it_syncs_recurring_order_dates_correctly(): void
    {
        Carbon::setTestNow(now());
        Setting::updateOrCreate(['key' => 'order_deadline_hour'],['value' => 11]);

        $schedule = Schedule::factory()->create();

        $date = Date::factory()->create([
            'schedule_id' => $schedule->id,
            'pickup_date' => today()->addDays(10),
            'order_end_date' => today()->addDays(8)
        ]);

        $expectedDate = Date::factory()->create([
            'schedule_id' => $schedule->id,
            'pickup_date' => today()->addDays(21),
            'order_end_date' => today()->addDays(19)
        ]);

        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);

        $subscription = RecurringOrder::factory()->create([
            'ready_at' => today()->addDays(3),
            'generate_at' => today()->addDay(),
            'reorder_frequency' => 7,
            'fulfillment_id' => $pickup->id
        ]);

        app(SyncSubscriptionDatetimes::class)->handle($subscription, $expectedDate->pickup_date);

        $this->assertDatabaseHas(RecurringOrder::class, [
            'id' => $subscription->id,
            'ready_at' => $expectedDate->pickup_date->setTime(0,0,0)->format('Y-m-d H:i:s'),
            'generate_at' => $expectedDate->order_end_date->subDays(3)->setTime(11,0,0)->format('Y-m-d H:i:s'),
        ]);

        Carbon::setTestNow();
    }
}
