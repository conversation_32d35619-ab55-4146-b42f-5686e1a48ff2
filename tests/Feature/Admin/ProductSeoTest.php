<?php

namespace Tests\Feature\Admin;

use App\Models\Product;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class ProductSeoTest extends TenantTestCase
{
    #[Test]
    public function a_guest_cannot_update_product_seo(): void
    {
        $product = Product::factory()->create();

        $this->put(route('admin.products.update.seo', compact('product')))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function only_admins_can_update_product_seo(): void
    {
        $product = Product::factory()->create();

        $this->actingAsCustomer()
            ->put(route('admin.products.update.seo', compact('product')))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function an_invalid_product_cannot_be_updated(): void
    {
        $this->actingAsAdmin()
            ->put(route('admin.products.update.seo', ['abc']))
            ->assertRedirect('/');
    }

    #[Test]
    public function it_validates_the_product_seo_update_request(): void
    {
        $product = Product::factory()->create();

        $this->actingAsAdmin()
            ->put(route('admin.products.update.seo', compact('product')), [
                'seo_description' => 123,
                'seo_visibility' => 123,
                'canonical_url' => 123,
                'page_title' => 123,
                'head_tags' => 123,
                'body_tags' => 123,
            ])
            ->assertInvalid([
                'seo_description' => 'The seo description field must be a string.',
                'seo_visibility' => 'The seo visibility field must be true or false.',
                'canonical_url' => 'The canonical url field must be a string.',
                'page_title' => 'The page title field must be a string.',
                'head_tags' => 'The head tags field must be a string.',
                'body_tags' => 'The body tags field must be a string.',
            ]);
    }

    #[Test]
    public function it_updates_seo_fields_to_values(): void
    {
        $product = Product::factory()->create();

        $this->actingAsAdmin()
            ->put(route('admin.products.update.seo', compact('product')), [
                'seo_description' => 'description',
                'seo_visibility' => 1,
                'canonical_url' => 'canonical url',
                'page_title' => 'page title',
                'head_tags' => 'head tags',
                'body_tags' => 'body tags',
            ])
            ->assertSessionHasNoErrors();

        $this->assertDatabaseHas(Product::class, [
            'id' => $product->id,
            'seo_description' => 'description',
            'seo_visibility' => 1,
            'canonical_url' => 'canonical url',
            'page_title' => 'page title',
            'head_tags' => 'head tags',
            'body_tags' => 'body tags',
        ]);
    }

    #[Test]
    public function it_updates_seo_fields_to_blank(): void
    {
        $product = Product::factory()->create();

        $this->actingAsAdmin()
            ->put(route('admin.products.update.seo', compact('product')), [
                'seo_description' => '',
                'seo_visibility' => 0,
                'canonical_url' => '',
                'page_title' => '',
                'head_tags' => '',
                'body_tags' => '',
            ])
            ->assertSessionHasNoErrors();

        $this->assertDatabaseHas(Product::class, [
            'id' => $product->id,
            'seo_description' => null,
            'seo_visibility' => 0,
            'canonical_url' => null,
            'page_title' => null,
            'head_tags' => null,
            'body_tags' => null,
        ]);
    }
}
