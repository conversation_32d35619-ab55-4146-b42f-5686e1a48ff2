<?php

namespace Tests\Feature\Admin;

use App\Actions\Order\ApplyCoupon;
use App\Exceptions\CouponInvalidException;
use App\Models\Coupon;
use App\Models\Order;
use App\Models\User;
use Mockery;
use Mo<PERSON>y\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class OrderDiscountsTest extends TenantTestCase
{
    #[Test]
    public function it_requires_an_authenticated_user_to_store_an_order_discount(): void
    {
        $order = Order::factory()->create();

        $this->post(route('orders.discounts.store', compact('order')))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function it_requires_an_admin_to_store_an_order_discount(): void
    {
        $order = Order::factory()->create();

        $this->actingAsCustomer()
            ->post(route('orders.discounts.store', compact('order')))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function it_requires_a_valid_order_to_store_an_order_discount(): void
    {
        $this->actingAsAdmin()
            ->get(route('dashboard'))
            ->assertOk();

         $this->post(route('orders.discounts.store', ['order' => 'abc']))
            ->assertRedirect(route('dashboard'));
    }

    #[Test]
    public function it_validates_the_order_discount_store_request(): void
    {
        $order = Order::factory()->create();

        $this->actingAsAdmin()
            ->get(route('dashboard'))
            ->assertOk();

        $this->post(route('orders.discounts.store', compact('order')))
            ->assertRedirect(route('dashboard'))
            ->assertSessionHasErrors(['code' => 'The code field is required.']);

        $this->post(route('orders.discounts.store', compact('order')), [
            'code' => 'abc'
        ])
            ->assertRedirect(route('dashboard'))
            ->assertSessionHasErrors(['code' => 'The selected code is invalid.']);
    }

    #[Test]
    public function it_returns_an_error_when_the_invalid_coupon_exception_is_thrown_while_applying_discount(): void
    {
        $order = Order::factory()->create();
        $coupon = Coupon::factory()->create();

        $this->mock(ApplyCoupon::class, function (MockInterface $mock) use ($order, $coupon) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(
                    Mockery::on(fn (Order $arg) => $arg->id === $order->id),
                    Mockery::on(fn (Coupon $arg) => $arg->id === $coupon->id)
                )
                ->andThrow(new CouponInvalidException('Coupon error message'));
        });

        $this->actingAsAdmin()
            ->post(route('orders.discounts.store', compact('order')), [
            'code' => $coupon->code
        ])
            ->assertStatus(422)
            ->assertJsonFragment(['Coupon error message']);
    }

    #[Test]
    public function it_can_apply_a_discount_to_an_order(): void
    {
        $order = Order::factory()->create();
        $coupon = Coupon::factory()->create();

        $this->mock(ApplyCoupon::class, function (MockInterface $mock) use ($order, $coupon) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(
                    Mockery::on(fn (Order $arg) => $arg->id === $order->id),
                    Mockery::on(fn (Coupon $arg) => $arg->id === $coupon->id)
                )
                ->andReturn($coupon);
        });

        $this->actingAsAdmin()
            ->post(route('orders.discounts.store', compact('order')), [
                'code' => $coupon->code
            ])
            ->assertStatus(200)
            ->assertJsonFragment(['responseText' => 'Coupon added.']);
    }

    #[Test]
    public function it_cannot_apply_a_discount_to_an_order_that_is_already_paid(): void
    {
        $order = Order::factory()->create(['paid' => true]);
        $coupon = Coupon::factory()->create();

        $this->mock(ApplyCoupon::class, function (MockInterface $mock) {
            $mock->shouldNotReceive('handle');
        });

        $this->actingAsAdmin()
            ->post(route('orders.discounts.store', compact('order')), [
                'code' => $coupon->code
            ])
            ->assertBadRequest()
            ->assertJsonFragment(['A discount cannot be added to an order that is already paid.']);
    }

    #[Test]
    public function it_can_remove_a_discount_from_an_order(): void
    {
        $admin = User::factory()->admin()->create();

        $order = Order::factory()->create();
        $coupon = Coupon::factory()->create();
        $order->discounts()->attach($coupon->id, ['user_id' => $admin->id, 'savings' => 500]);

        $this->actingAs($admin)
            ->delete(route('orders.discounts.destroy', [$order->id, $coupon->id]), [
                'code' => $coupon->code
            ])
            ->assertOk()
            ->assertJsonStructure(['order', 'responseText']);

        $this->assertDatabaseMissing('coupon_order', [
            'coupon_id' => $coupon->id,
            'order_id' => $order->id,
        ]);
    }

    #[Test]
    public function it_cannot_remove_a_discount_from_an_order_that_is_already_paid(): void
    {
        $admin = User::factory()->admin()->create();

        $order = Order::factory()->create(['paid' => true]);
        $coupon = Coupon::factory()->create();
        $order->discounts()->attach($coupon->id, ['user_id' => $admin->id, 'savings' => 500]);

        $this->actingAs($admin)
            ->delete(route('orders.discounts.destroy', [$order->id, $coupon->id]), [
                'code' => $coupon->code
            ])
            ->assertBadRequest()
            ->assertJsonFragment(['A discount cannot be removed from an order that is already paid.']);
    }
}