<?php

namespace Tests\Feature\Admin;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Payment;
use App\Models\Pickup;
use App\Models\Product;
use App\Models\RecurringOrder;
use App\Models\Schedule;
use App\Models\Setting;
use App\Models\Tag;
use App\Models\Template;
use App\Models\User;
use App\Support\Enums\Channel;
use App\Support\Enums\OrderStatus;
use Carbon\Carbon;
use Illuminate\Pagination\LengthAwarePaginator;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class OrderTest extends TenantTestCase
{
    #[Test]
    public function a_guest_cannot_view_admin_orders(): void
    {
        $this->get(route('admin.orders.index'))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function a_non_admin_cannot_view_admin_orders(): void
    {
        $this->actingAsCustomer()
            ->get(route('admin.orders.index'))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function an_admin_can_view_orders(): void
    {
        $this->actingAsAdmin()
            ->get(route('admin.orders.index'))
            ->assertOk()
            ->assertViewIs('orders.index')
            ->assertViewHasAll([
                'orders', 'orderTags', 'savedFilters', 'appliedFilter', 'appliedFilters', 'total', 'canBulkProcessOrders'
            ]);
    }

    #[Test]
    public function it_returns_confirmed_order_only_by_default(): void
    {
        /** @var Order $order_one */
        $order_one = Order::factory()->create([
            'confirmed' => false
        ]);

        /** @var Order $order_two */
        $order_two = Order::factory()->create([
            'confirmed' => true
        ]);

        $this->actingAsAdmin()
            ->get(route('admin.orders.index'))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($order_two, $order_one) {
                return $arg->contains(fn(Order $order) => $order->id === $order_two->id)
                    && $arg->doesntContain(fn(Order $order) => $order->id === $order_one->id);
            });
    }

    #[Test]
    public function it_sorts_by_descending_confirmed_date_by_default(): void
    {
        /** @var Order $order_one */
        $order_one = Order::factory()->create([
            'confirmed_date' => today()->addDay(),
            'confirmed' => true,
            'created_at' => now()
        ]);

        /** @var Order $order_two */
        $order_two = Order::factory()->create([
            'confirmed_date' => today()->addDays(3),
            'confirmed' => true,
            'created_at' => now()->addSecond()
        ]);

        $this->actingAsAdmin()
            ->get(route('admin.orders.index'))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($order_one, $order_two) {
                $orderIds = $arg->map(function (Order $order) {
                    return $order->id;
                });

                return $orderIds->search($order_two->id) < $orderIds->search($order_one->id);
            });
    }

    #[Test]
    public function it_can_sort_orders_by_attributes(): void
    {
        /** @var Order $order_one */
        $order_one = Order::factory()->create([
            'pickup_date' => today()->addDay(),
            'status_id' => 1,
            'confirmed' => true,
            'created_at' => now()
        ]);

        /** @var Order $order_two */
        $order_two = Order::factory()->create([
            'pickup_date' => today()->addDays(3),
            'status_id' => 2,
            'confirmed' => true,
            'created_at' => now()->addSecond()
        ]);

        $this->actingAsAdmin()
            ->get(route('admin.orders.index', ['orderBy' => 'pickup_date', 'sort' => 'asc']))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($order_one, $order_two) {
                $orderIds = $arg->map(function (Order $order) {
                    return $order->id;
                });

                return $orderIds->search($order_one->id) < $orderIds->search($order_two->id);
            });

        $this->get(route('admin.orders.index', ['orderBy' => 'pickup_date', 'sort' => 'desc']))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($order_one, $order_two) {
                $orderIds = $arg->map(function (Order $order) {
                    return $order->id;
                });

                return $orderIds->search($order_two->id) < $orderIds->search($order_one->id);
            });

        $this->get(route('admin.orders.index', ['orderBy' => 'status_id', 'sort' => 'asc']))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($order_one, $order_two) {
                $orderIds = $arg->map(function (Order $order) {
                    return $order->id;
                });

                return $orderIds->search($order_one->id) < $orderIds->search($order_two->id);
            });

        $this->get(route('admin.orders.index', ['orderBy' => 'status_id', 'sort' => 'desc']))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($order_one, $order_two) {
                $orderIds = $arg->map(function (Order $order) {
                    return $order->id;
                });

                return $orderIds->search($order_two->id) < $orderIds->search($order_one->id);
            });
    }

    #[Test]
    public function it_sorts_by_asc_when_using_invalid_sort_attribute(): void
    {
        /** @var Order $order_one */
        $order_one = Order::factory()->create([
            'pickup_date' => today()->addDay(),
            'status_id' => 1,
            'confirmed' => true,
            'created_at' => now()
        ]);

        /** @var Order $order_two */
        $order_two = Order::factory()->create([
            'pickup_date' => today()->addDays(3),
            'status_id' => 2,
            'confirmed' => true,
            'created_at' => now()->addSecond()
        ]);

        $this->actingAsAdmin()
            ->get(route('admin.orders.index', ['orderBy' => 'pickup_date', 'sort' => 'abc']))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($order_one, $order_two) {
                $orderIds = $arg->map(fn(Order $order) => $order->id);

                return $orderIds->search($order_one->id) < $orderIds->search($order_two->id);
            });
    }

    #[Test]
    public function an_admin_can_filter_orders_by_customer_name(): void
    {
        Order::factory()->create(['confirmed' => true]);
        $expected = Order::factory()->create(['confirmed' => true, 'customer_first_name' => 'test', 'customer_last_name' => 'tester']);

        $this->actingAsAdmin()
            ->get(route('admin.orders.index', ['orders' => 'test tester']))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($expected) {
                return $arg->contains(fn(Order $order) => $order->id === $expected->id);
            });
    }

    #[Test]
    public function an_admin_can_filter_orders_by_customer_name_with_apostrophe(): void
    {
        Order::factory()->create(['confirmed' => true]);
        $expected = Order::factory()->create(['confirmed' => true, 'customer_first_name' => 'Test\'s', 'customer_last_name' => 'tester']);

        $this->actingAsAdmin()
            ->get(route('admin.orders.index', ['orders' => 'est\'']))
            ->assertOk()
            ->assertViewHas(
                'orders',
                fn(LengthAwarePaginator $arg) =>
                    $arg->contains(fn(Order $order) => $order->id === $expected->id));
    }

    #[Test]
    public function an_admin_can_filter_orders_by_customer_phone(): void
    {
        Order::factory()->create(['confirmed' => true]);
        $expected = Order::factory()->create(['confirmed' => true, 'customer_phone' => '************']);

        $this->actingAsAdmin()
            ->get(route('admin.orders.index', ['orders' => '************']))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($expected) {
                return $arg->contains(fn(Order $order) => $order->id === $expected->id);
            });
    }

    #[Test]
    public function an_admin_can_filter_orders_by_customer_email(): void
    {
        Order::factory()->create(['confirmed' => true]);
        $expected = Order::factory()->create(['confirmed' => true, 'customer_email' => '<EMAIL>']);

        $this->actingAsAdmin()
            ->get(route('admin.orders.index', ['orders' => '<EMAIL>']))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($expected) {
                return $arg->contains(fn(Order $order) => $order->id === $expected->id);

            });
    }

    #[Test]
    public function an_admin_can_filter_orders_by_id(): void
    {
        Order::factory()->create(['confirmed' => true]);
        $expected = Order::factory()->create(['confirmed' => true]);

        $this->actingAsAdmin()
            ->get(route('admin.orders.index', ['orders' => $expected->id]))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($expected) {
                return $arg->contains(fn(Order $order) => $order->id === $expected->id);
            });
    }

    #[Test]
    public function an_admin_can_filter_orders_by_order_status(): void
    {
        $order_one = Order::factory()->create(['confirmed' => true, 'status_id' => 1]);
        $order_two = Order::factory()->create(['confirmed' => true, 'status_id' => 2]);
        $order_three = Order::factory()->create(['confirmed' => true, 'status_id' => 3]);

        $this->actingAsAdmin()
            ->get(route('admin.orders.index', ['order_status' => 1]))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($order_one, $order_two, $order_three) {
                return $arg->contains(fn(Order $order) => $order->id === $order_one->id)
                    && $arg->doesntContain(fn(Order $order) => $order->id === $order_two->id)
                    && $arg->doesntContain(fn(Order $order) => $order->id === $order_three->id);
            });

        $this->get(route('admin.orders.index', ['order_status' => 'new']))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($order_one, $order_two, $order_three) {
                return $arg->contains(fn(Order $order) => $order->id === $order_one->id)
                    && $arg->doesntContain(fn(Order $order) => $order->id === $order_two->id)
                    && $arg->doesntContain(fn(Order $order) => $order->id === $order_three->id);
            });

        $this->get(route('admin.orders.index', ['order_status' => [2, 3]]))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($order_one, $order_two, $order_three) {
                return $arg->doesntContain(fn(Order $order) => $order->id === $order_one->id)
                    && $arg->contains(fn(Order $order) => $order->id === $order_two->id)
                    && $arg->contains(fn(Order $order) => $order->id === $order_three->id);
            });

        $this->get(route('admin.orders.index', ['order_status' => [2, 3]]))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($order_one, $order_two, $order_three) {
                return $arg->doesntContain(fn(Order $order) => $order->id === $order_one->id)
                    && $arg->contains(fn(Order $order) => $order->id === $order_two->id)
                    && $arg->contains(fn(Order $order) => $order->id === $order_three->id);
            });

        $this->get(route('admin.orders.index', ['order_status' => ['processing', 'packed']]))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($order_one, $order_two, $order_three) {
                return $arg->doesntContain(fn(Order $order) => $order->id === $order_one->id)
                    && $arg->contains(fn(Order $order) => $order->id === $order_two->id)
                    && $arg->contains(fn(Order $order) => $order->id === $order_three->id);
            });
    }

    #[Test]
    public function an_admin_can_filter_orders_by_status(): void
    {
        $order_one = Order::factory()->create(['confirmed' => true, 'status_id' => 1]);
        $order_two = Order::factory()->create(['confirmed' => true, 'status_id' => 2]);
        $order_three = Order::factory()->create(['confirmed' => true, 'status_id' => 3]);

        $this->actingAsAdmin()
            ->get(route('admin.orders.index', ['status' => 1]))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($order_one, $order_two, $order_three) {
                return $arg->contains(fn(Order $order) => $order->id === $order_one->id)
                    && $arg->doesntContain(fn(Order $order) => $order->id === $order_two->id)
                    && $arg->doesntContain(fn(Order $order) => $order->id === $order_three->id);
            });

        $this->actingAsAdmin()
            ->get(route('admin.orders.index', ['status' => 'new']))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($order_one, $order_two, $order_three) {
                return $arg->contains(fn(Order $order) => $order->id === $order_one->id)
                    && $arg->doesntContain(fn(Order $order) => $order->id === $order_two->id)
                    && $arg->doesntContain(fn(Order $order) => $order->id === $order_three->id);
            });

        $this->get(route('admin.orders.index', ['status' => [2, 3]]))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($order_one, $order_two, $order_three) {
                return $arg->doesntContain(fn(Order $order) => $order->id === $order_one->id)
                    && $arg->contains(fn(Order $order) => $order->id === $order_two->id)
                    && $arg->contains(fn(Order $order) => $order->id === $order_three->id);
            });

        $this->get(route('admin.orders.index', ['status' => [2, 3]]))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($order_one, $order_two, $order_three) {
                return $arg->doesntContain(fn(Order $order) => $order->id === $order_one->id)
                    && $arg->contains(fn(Order $order) => $order->id === $order_two->id)
                    && $arg->contains(fn(Order $order) => $order->id === $order_three->id);
            });

        $this->get(route('admin.orders.index', ['status' => ['processing', 'packed']]))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($order_one, $order_two, $order_three) {
                return $arg->doesntContain(fn(Order $order) => $order->id === $order_one->id)
                    && $arg->contains(fn(Order $order) => $order->id === $order_two->id)
                    && $arg->contains(fn(Order $order) => $order->id === $order_three->id);
            });
    }

    #[Test]
    public function an_admin_can_filter_orders_by_schedule(): void
    {
        $order_one = Order::factory()->create(['confirmed' => true]);
        $schedule_one = Schedule::factory()->create();
        $order_two = Order::factory()->create(['confirmed' => true, 'schedule_id' => $schedule_one->id]);
        $schedule_two = Schedule::factory()->create();
        $order_three = Order::factory()->create(['confirmed' => true, 'schedule_id' => $schedule_two->id]);

        $this->actingAsAdmin()
            ->get(route('admin.orders.index', ['schedule_id' => $schedule_one->id]))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($order_one, $order_two, $order_three) {
                return $arg->contains(fn(Order $order) => $order->id === $order_two->id)
                    && $arg->doesntContain(fn(Order $order) => $order->id === $order_one->id)
                    && $arg->doesntContain(fn(Order $order) => $order->id === $order_three->id);
            });

        $this->get(route('admin.orders.index', ['schedule_id' => [$schedule_one->id, $schedule_two->id]]))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($order_one, $order_two, $order_three) {
                return $arg->contains(fn(Order $order) => $order->id === $order_two->id)
                    && $arg->doesntContain(fn(Order $order) => $order->id === $order_one->id)
                    && $arg->contains(fn(Order $order) => $order->id === $order_three->id);
            });

    }

    #[Test]
    public function an_admin_can_filter_orders_by_packed_by(): void
    {
        Order::factory()->create(['confirmed' => true]);
        $user = User::factory()->create();
        $expected = Order::factory()->create(['confirmed' => true, 'staff_id' => $user->id]);

        $this->actingAsAdmin()
            ->get(route('admin.orders.index', ['packed_by' => $user->id]))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($expected) {
                return $arg->contains(fn(Order $order) => $order->id === $expected->id);
            });
    }

    #[Test]
    public function an_admin_can_filter_orders_by_confirmed_date(): void
    {
        Carbon::setTestNow(now());

        $order_one = Order::factory()->create(['confirmed' => true, 'confirmed_date' => today()->format('Y-m-d')]);
        $order_two = Order::factory()->create(['confirmed' => true, 'confirmed_date' => today()->addDays(2)->format('Y-m-d')]);

        $this->actingAsAdmin()
            ->get(route('admin.orders.index', ['confirmed_date' => today()->addDays(2)->format('Y-m-d')]))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($order_two) {
                return $arg->contains(fn(Order $order) => $order->id === $order_two->id);
            });

        $this->get(route('admin.orders.index', ['confirmed_date' => [
            'start' => today()->addDays(2)->format('Y-m-d'),
        ]]))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($order_two) {
                return $arg->contains(fn(Order $order) => $order->id === $order_two->id);
            });

        $this->get(route('admin.orders.index', ['confirmed_date' => [
            'end' => today()->format('Y-m-d')
        ]]))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($order_one) {
                return $arg->contains(fn(Order $order) => $order->id === $order_one->id);
            });

        Carbon::setTestNow();
    }

    #[Test]
    public function an_admin_can_filter_orders_by_pickup(): void
    {
        Order::factory()->create(['confirmed' => true]);
        $pickup = Pickup::factory()->create();
        $expected = Order::factory()->create(['confirmed' => true, 'pickup_id' => $pickup->id]);

        $this->actingAsAdmin()
            ->get(route('admin.orders.index', ['pickup_id' => $pickup->id]))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($expected) {
                return $arg->contains(fn(Order $order) => $order->id === $expected->id);
            });
    }

    #[Test]
    public function an_admin_can_filter_orders_by_confirmation_date_start(): void
    {
        Carbon::setTestNow(now());

        Order::factory()->create(['confirmed' => true, 'confirmed_date' => today()->format('Y-m-d')]);
        $expected = Order::factory()->create(['confirmed' => true, 'confirmed_date' => today()->addDays(2)->format('Y-m-d')]);

        $this->actingAsAdmin()
            ->get(route('admin.orders.index', ['confirmation_date_start' => today()->addDays(2)->format('Y-m-d')]))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($expected) {
                return $arg->contains(fn(Order $order) => $order->id === $expected->id);
            });

        Carbon::setTestNow();
    }

    #[Test]
    public function an_admin_can_filter_orders_by_confirmation_date_end(): void
    {
        Carbon::setTestNow(now());

        Order::factory()->create(['confirmed' => true, 'confirmed_date' => today()->addDays(2)->format('Y-m-d')]);
        $expected = Order::factory()->create(['confirmed' => true, 'confirmed_date' => today()->format('Y-m-d')]);

        $this->actingAsAdmin()
            ->get(route('admin.orders.index', ['confirmation_date_end' => today()->format('Y-m-d')]))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($expected) {
                return $arg->contains(fn(Order $order) => $order->id === $expected->id);
            });

        Carbon::setTestNow();
    }

    #[Test]
    public function an_admin_can_filter_orders_by_pack_deadline_at_start(): void
    {
        Carbon::setTestNow(now());

        Order::factory()->create(['confirmed' => true, 'pack_deadline_at' => today()->format('Y-m-d')]);
        $expected = Order::factory()->create(['confirmed' => true, 'pack_deadline_at' => today()->addDays(2)->format('Y-m-d')]);

        $this->actingAsAdmin()
            ->get(route('admin.orders.index', ['pack_deadline_at_start' => today()->addDays(2)->format('Y-m-d')]))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($expected) {
                return $arg->contains(fn(Order $order) => $order->id === $expected->id);
            });

        Carbon::setTestNow();
    }

    #[Test]
    public function an_admin_can_filter_orders_by_pack_deadline_at_end(): void
    {
        Carbon::setTestNow(now());

        Order::factory()->create(['confirmed' => true, 'pack_deadline_at' => today()->addDays(2)->format('Y-m-d')]);
        $expected = Order::factory()->create(['confirmed' => true, 'pack_deadline_at' => today()->format('Y-m-d')]);

        $this->actingAsAdmin()
            ->get(route('admin.orders.index', ['pack_deadline_at_end' => today()->format('Y-m-d')]))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($expected) {
                return $arg->contains(fn(Order $order) => $order->id === $expected->id);
            });

        Carbon::setTestNow();
    }

    #[Test]
    public function an_admin_can_filter_orders_by_pickup_date(): void
    {
        Carbon::setTestNow(now());

        $order_one = Order::factory()->create(['confirmed' => true, 'pickup_date' => today()->format('Y-m-d')]);
        $order_two = Order::factory()->create(['confirmed' => true, 'pickup_date' => today()->addDays(2)->format('Y-m-d')]);

        $this->actingAsAdmin()
            ->get(route('admin.orders.index', ['pickup_date' => today()->addDays(2)->format('Y-m-d')]))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($order_two) {
                return $arg->contains(fn(Order $order) => $order->id === $order_two->id);
            });

        $this->get(route('admin.orders.index', ['pickup_date' => [
            'start' => today()->addDays(2)->format('Y-m-d'),
        ]]))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($order_two) {
                return $arg->contains(fn(Order $order) => $order->id === $order_two->id);
            });

        $this->get(route('admin.orders.index', ['pickup_date' => [
            'end' => today()->format('Y-m-d')
        ]]))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($order_one) {
                return $arg->contains(fn(Order $order) => $order->id === $order_one->id);
            });

        Carbon::setTestNow();
    }

    #[Test]
    public function an_admin_can_filter_orders_by_payment_date(): void
    {
        Carbon::setTestNow(now());

        $order_one = Order::factory()->create(['confirmed' => true, 'payment_date' => today()->format('Y-m-d')]);
        $order_two = Order::factory()->create(['confirmed' => true, 'payment_date' => today()->addDays(2)->format('Y-m-d')]);

        $this->actingAsAdmin()
            ->get(route('admin.orders.index', [
            'payment_date' => [
                'start' => today()->addDays(2)->format('Y-m-d'),
                ]
        ]))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($order_two) {
                return $arg->contains(fn(Order $order) => $order->id === $order_two->id);
            });

        $this->get(route('admin.orders.index', ['payment_date' => [
            'end' => today()->format('Y-m-d')
        ]]))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($order_one) {
                return $arg->contains(fn(Order $order) => $order->id === $order_one->id);
            });

        Carbon::setTestNow();
    }

    #[Test]
    public function an_admin_can_filter_orders_by_paid(): void
    {
        $not_paid = Order::factory()->create(['confirmed' => true, 'paid' => false]);
        $paid = Order::factory()->create(['confirmed' => true, 'paid' => true]);

        $this->actingAsAdmin()
            ->get(route('admin.orders.index', ['paid' => 0]))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($not_paid, $paid) {
                return $arg->contains(fn(Order $order) => $order->id === $not_paid->id)
                    && $arg->doesntContain(fn(Order $order) => $order->id === $paid->id);
            });

        $this->get(route('admin.orders.index', ['paid' => 1]))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($paid, $not_paid) {
                return $arg->contains(fn(Order $order) => $order->id === $paid->id)
                    && $arg->doesntContain(fn(Order $order) => $order->id === $not_paid->id);
            });

        $this->get(route('admin.orders.index', ['paid' => 'all']))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($not_paid, $paid) {
                return $arg->contains(fn(Order $order) => $order->id === $paid->id)
                    && $arg->contains(fn(Order $order) => $order->id === $not_paid->id);
            });
    }

    #[Test]
    public function an_admin_can_filter_orders_by_flagged(): void
    {
        Order::factory()->create(['confirmed' => true, 'flagged' => false]);
        $expected = Order::factory()->create(['confirmed' => true, 'flagged' => true]);

        $this->actingAsAdmin()
            ->get(route('admin.orders.index', ['flagged' => 1]))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($expected) {
                return $arg->contains(fn(Order $order) => $order->id === $expected->id);
            });
    }

    #[Test]
    public function an_admin_can_filter_orders_by_fulfillment_error(): void
    {
        Order::factory()->create(['confirmed' => true, 'fulfillment_error' => false]);
        $expected = Order::factory()->create(['confirmed' => true, 'fulfillment_error' => true]);

        $this->actingAsAdmin()
            ->get(route('admin.orders.index', ['fulfillment_error' => 1]))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($expected) {
                return $arg->contains(fn(Order $order) => $order->id === $expected->id);
            });
    }

    #[Test]
    public function an_admin_can_filter_orders_by_picked_up(): void
    {
        Order::factory()->create(['confirmed' => true, 'picked_up' => false]);
        $expected = Order::factory()->create(['confirmed' => true, 'picked_up' => true]);

        $this->actingAsAdmin()
            ->get(route('admin.orders.index', ['picked_up' => 1]))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($expected) {
                return $arg->contains(fn(Order $order) => $order->id === $expected->id);
            });
    }

    #[Test]
    public function an_admin_can_filter_orders_by_exported(): void
    {
        Order::factory()->create(['confirmed' => true, 'exported' => false]);
        $expected = Order::factory()->create(['confirmed' => true, 'exported' => true]);

        $this->actingAsAdmin()
            ->get(route('admin.orders.index', ['exported' => 1]))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($expected) {
                return $arg->contains(fn(Order $order) => $order->id === $expected->id);
            });
    }

    #[Test]
    public function an_admin_can_filter_orders_by_payment_id(): void
    {
        Order::factory()->create(['confirmed' => true, 'payment_id' => false]);
        $payment = Payment::factory()->create();
        $expected = Order::factory()->create(['confirmed' => true, 'payment_id' => $payment->id]);

        $this->actingAsAdmin()
            ->get(route('admin.orders.index', ['payment_id' => $payment->id]))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($expected) {
                return $arg->contains(fn(Order $order) => $order->id === $expected->id);
            });
    }

    #[Test]
    public function an_admin_can_filter_orders_by_first_time_order(): void
    {
        Order::factory()->create(['confirmed' => true, 'first_time_order' => false]);
        $expected = Order::factory()->create(['confirmed' => true, 'first_time_order' => true]);

        $this->actingAsAdmin()
            ->get(route('admin.orders.index', ['first_time_order' => 1]))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($expected) {
                return $arg->contains(fn(Order $order) => $order->id === $expected->id);
            });
    }

    #[Test]
    public function an_admin_can_filter_orders_by_confirmed(): void
    {
        $not_confirmed = Order::factory()->create(['confirmed' => false, 'subtotal' => 1]);
        $confirmed = Order::factory()->create(['confirmed' => true]);

        $this->actingAsAdmin()
            ->get(route('admin.orders.index', ['confirmed' => false]))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($not_confirmed, $confirmed) {
                return $arg->contains(fn(Order $order) => $order->id === $not_confirmed->id)
                    && $arg->doesntContain(fn(Order $order) => $order->id === $confirmed->id);
            });

        $this->get(route('admin.orders.index', ['confirmed' => 'unconfirmed']))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($not_confirmed, $confirmed) {
                return $arg->contains(fn(Order $order) => $order->id === $not_confirmed->id)
                    && $arg->doesntContain(fn(Order $order) => $order->id === $confirmed->id);;
            });

        $this->get(route('admin.orders.index', ['confirmed' => true]))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($confirmed, $not_confirmed) {
                return $arg->contains(fn(Order $order) => $order->id === $confirmed->id)
                    && $arg->doesntContain(fn(Order $order) => $order->id === $not_confirmed->id);
            });

        $this->get(route('admin.orders.index', ['confirmed' => 'confirmed']))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($confirmed, $not_confirmed) {
                return $arg->contains(fn(Order $order) => $order->id === $confirmed->id)
                    && $arg->doesntContain(fn(Order $order) => $order->id === $not_confirmed->id);
            });

        $this->get(route('admin.orders.index', ['confirmed' => 'all']))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($not_confirmed, $confirmed) {
                return $arg->contains(fn(Order $order) => $order->id === $confirmed->id)
                    && $arg->contains(fn(Order $order) => $order->id === $not_confirmed->id);
            });
    }

    #[Test]
    public function an_admin_can_filter_orders_by_order_type_id(): void
    {
        Order::factory()->create(['confirmed' => true, 'type_id' => 1]);
        $expected = Order::factory()->create(['confirmed' => true, 'type_id' => 2]);

        $this->actingAsAdmin()
            ->get(route('admin.orders.index', ['order_type_id' => 2]))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($expected) {
                return $arg->contains(fn(Order $order) => $order->id === $expected->id);
            });

        $this->actingAsAdmin()
            ->get(route('admin.orders.index', ['order_type_id' => [2]]))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($expected) {
                return $arg->contains(fn(Order $order) => $order->id === $expected->id);
            });
    }

    #[Test]
    public function an_admin_can_filter_orders_by_staff_id(): void
    {
        Order::factory()->create(['confirmed' => true]);
        $staff = User::factory()->create();
        $expected = Order::factory()->create(['confirmed' => true, 'staff_id' => $staff->id]);

        $this->actingAsAdmin()
            ->get(route('admin.orders.index', ['staff_id' => $staff->id]))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($expected) {
                return $arg->contains(fn(Order $order) => $order->id === $expected->id);
            });
    }

    #[Test]
    public function an_admin_can_filter_orders_by_order_tags(): void
    {
        $tag_one = Tag::factory()->create();
        $tag_two = Tag::factory()->create();
        $order_one = Order::factory()->create(['confirmed' => true]);
        $order_one->tags()->attach($tag_one->id);
        $order_two = Order::factory()->create(['confirmed' => true]);
        $order_two->tags()->attach($tag_one->id);
        $order_two->tags()->attach($tag_two->id);

        $this->actingAsAdmin()
            ->get(route('admin.orders.index', ['order_tags' => $tag_one->id]))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($order_one, $order_two) {
                return $arg->contains(fn(Order $order) => $order->id === $order_one->id)
                    && $arg->contains(fn(Order $order) => $order->id === $order_two->id);
            });

        $this->actingAsAdmin()
            ->get(route('admin.orders.index', ['order_tags' => $tag_one->id]))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($order_one, $order_two) {
                return $arg->contains(fn(Order $order) => $order->id === $order_one->id)
                    && $arg->contains(fn(Order $order) => $order->id === $order_two->id);
            });

        $this->get(route('admin.orders.index', ['order_tags' => [$tag_two->id]]))
            ->assertOk()
            ->assertViewHas('orders', function (LengthAwarePaginator $arg) use ($order_two, $order_one) {
                return $arg->contains(fn(Order $order) => $order->id === $order_two->id)
                    && $arg->doesntContain(fn(Order $order) => $order->id === $order_one->id);
            });
    }

    #[Test]
    public function an_admin_can_filter_orders_by_subscription_status(): void
    {
        $one_off_order = Order::factory()->create(['is_recurring' => false, 'confirmed' => true]);
        $recurring_cart = Order::factory()->create(['is_recurring' => true, 'confirmed' => true]);

        $blueprint = RecurringOrder::factory()->create();
        $recurring_order = Order::factory()->create(['is_recurring' => true, 'blueprint_id' => $blueprint->id, 'confirmed' => true]);

        $this->actingAsAdmin()
            ->get(route('admin.orders.index', [
                'subscription_status' => 'recurring'
            ]))
            ->assertOk()
            ->assertViewIs('orders.index')
            ->assertViewHas('orders', function ($arg) use ($one_off_order, $recurring_cart, $recurring_order) {
                return $arg instanceof LengthAwarePaginator
                    && is_null($arg->firstWhere('id', $one_off_order->id))
                    && is_null($arg->firstWhere('id', $recurring_cart->id))
                    && !is_null($arg->firstWhere('id', $recurring_order->id));
            });
    }

    #[Test]
    public function a_guest_cannot_create_an_admin_order(): void
    {
        $this->post(route('admin.orders.store'))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function a_non_admin_cannot_create_an_admin_order(): void
    {
        $this->actingAsCustomer()
            ->post(route('admin.orders.store'))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function it_validates_the_admin_order_creation_request(): void
    {
        $this->actingAsAdmin()
            ->get(route('dashboard'))
            ->assertOk();

        $this->post(route('admin.orders.store'))
            ->assertRedirect(route('dashboard'))
            ->assertSessionHasErrors([
                'user_id' => 'The user id field is required.',
                'deadline_date' => 'The deadline date field is required.',
                'pickup_date' => 'The pickup date field is required.',
                'payment_id' => 'The payment id field is required.',
                'type_id' => 'The type id field is required.'
            ]);

        $this->post(route('admin.orders.store'), [
            'user_id' => 0
        ])
            ->assertRedirect(route('dashboard'))
            ->assertSessionHasErrors(['user_id' => 'The selected user id is invalid.']);

        $this->post(route('admin.orders.store'), [
            'deadline_date' => 'deadline',
            'pickup_date' => 'pickup',
        ])
            ->assertRedirect(route('dashboard'))
            ->assertSessionHasErrors([
                'deadline_date' => 'The deadline date field must be a valid date.',
                'pickup_date' => 'The pickup date field must be a valid date.',
            ]);

        $this->post(route('admin.orders.store'), [
            'payment_id' => 'deadline',
            'type_id' => 'pickup',
        ])
            ->assertRedirect(route('dashboard'))
            ->assertSessionHasErrors([
                'payment_id' => 'The payment id field must be an integer.',
                'type_id' => 'The type id field must be an integer.',
            ]);

        $this->post(route('admin.orders.store'), [
            'reorder' => true,
        ])
            ->assertRedirect(route('dashboard'))
            ->assertSessionHasErrors([
                'reorder_id' => 'The reorder id field is required when reorder is present.',
            ]);
    }

    #[Test]
    public function it_can_create_an_order_for_a_user_not_on_subscription(): void
    {
        Carbon::setTestNow(now());

        $user = User::factory()->create();
        $payment = Payment::factory()->create();

        $this->actingAsAdmin()
            ->get(route('dashboard'))
            ->assertOk();

        $response = $this->post(route('admin.orders.store'), [
            'user_id' => $user->id,
            'deadline_date' => now()->addDay()->format('m/d/Y'),
            'pickup_date' => now()->addDays(2)->format('m/d/Y'),
            'payment_id' => $payment->id,
            'type_id' => Channel::DISTRIBUTION->value,
        ])
            ->assertSessionDoesntHaveErrors();

        $this->assertDatabaseHas(Order::class, [
            'customer_id' => $user->id,
            'is_recurring' => null,
            'blueprint_id' => null,
            'deadline_date' => now()->addDay()->format('Y-m-d'),
            'pickup_date' => now()->addDays(2)->format('Y-m-d'),
            'payment_id' => $payment->id,
            'type_id' => Channel::DISTRIBUTION->value,
        ]);

        $new_order = Order::where([
            'customer_id' => $user->id,
            'is_recurring' => null,
            'blueprint_id' => null,
            'deadline_date' => now()->addDay()->format('Y-m-d'),
            'pickup_date' => now()->addDays(2)->format('Y-m-d'),
            'payment_id' => $payment->id,
            'type_id' => Channel::DISTRIBUTION->value,
        ])->first();

        $response->assertRedirect(route('admin.orders.edit', ['order' => $new_order]));

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_create_a_one_off_order_for_a_user_on_subscription(): void
    {
        Carbon::setTestNow(now());

        $user = User::factory()->create();
        $blueprint = RecurringOrder::factory()->create(['customer_id' => $user->id]);
        $order = Order::factory()->create(['is_recurring' => true, 'blueprint_id' => $blueprint->id]);

        $payment = Payment::factory()->create();

        $this->actingAsAdmin()
            ->get(route('dashboard'))
            ->assertOk();

        $response = $this->post(route('admin.orders.store'), [
            'user_id' => $user->id,
            'deadline_date' => now()->addDay()->format('m/d/Y'),
            'pickup_date' => now()->addDays(2)->format('m/d/Y'),
            'payment_id' => $payment->id,
            'type_id' => 1
        ])
            ->assertSessionDoesntHaveErrors();

        $this->assertDatabaseHas(Order::class, [
            'customer_id' => $user->id,
            'is_recurring' => null,
            'blueprint_id' => null,
            'deadline_date' => now()->addDay()->format('Y-m-d'),
            'pickup_date' => now()->addDays(2)->format('Y-m-d'),
            'payment_id' => $payment->id,
            'type_id' => 1,
        ]);

        $new_order = Order::where([
            'customer_id' => $user->id,
            'is_recurring' => null,
            'blueprint_id' => null,
            'deadline_date' => now()->addDay()->format('Y-m-d'),
            'pickup_date' => now()->addDays(2)->format('Y-m-d'),
            'payment_id' => $payment->id,
            'type_id' => 1,
        ])->first();

        $this->assertNotNull($new_order);
        $this->assertInstanceOf(Order::class, $new_order);
        $this->assertEquals(0, $new_order->items()->count());

        $response->assertRedirect(route('admin.orders.edit', ['order' => $new_order]));

        Carbon::setTestNow();
    }

    #[Test]
    public function all_requests_to_show_page_are_redirected_to_the_edit_page(): void
    {
        $order = Order::factory()->create();

        $this->actingAsAdmin()->get(route('admin.orders.show', compact('order')))
            ->assertRedirect(route('admin.orders.edit', compact('order')));
    }

    #[Test]
    public function a_guest_cannot_update_an_admin_order(): void
    {
        $order = Order::factory()->create();

        $this->put(route('admin.orders.update', compact('order')))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function a_non_admin_cannot_update_an_admin_order(): void
    {
        $order = Order::factory()->create();

        $this->actingAsCustomer()
            ->put(route('admin.orders.update', compact('order')))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function pre_orders_cannot_be_updated_to_new_status(): void
    {
        $order = Order::factory()->create(['status_id' => OrderStatus::preOrder()]);

        $this->actingAsAdmin()
            ->put(route('admin.orders.update', compact('order')), [
                'status_id' => OrderStatus::confirmed()
            ])
            ->assertRedirect()
            ->assertInvalid(['status_id' => 'Pre-orders cannot be put into "New" status.']);
    }

    #[Test]
    public function a_guest_cannot_delete_order(): void
    {
        $order = Order::factory()->create();

        $this->delete(route('admin.orders.destroy', compact('order')))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function a_non_admin_cannot_delete_an_order(): void
    {
        $order = Order::factory()->create();

        $this->actingAsCustomer()
            ->delete(route('admin.orders.destroy', compact('order')))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function an_admin_cannot_delete_an_invalid_order(): void
    {
        $this->actingAsAdmin()
            ->delete(route('admin.orders.destroy', ['order' => 1278368123]))
            ->assertRedirect();
    }

    #[Test]
    public function an_admin_can_delete_an_order(): void
    {
        $order = Order::factory()->create(['confirmed' => true, 'paid' => true]);

        $this->actingAsAdmin()
            ->delete(route('admin.orders.destroy', compact('order')))
            ->assertRedirect();

        $this->assertDatabaseHas(Order::class, ['id' => $order->id]);
    }

    #[Test]
    public function an_admin_can_not_delete_a_paid_order(): void
    {
        $order = Order::factory()->create();
        $items = OrderItem::factory(2)->create(['order_id' => $order->id]);

        $this->actingAsAdmin()
            ->delete(route('admin.orders.destroy', compact('order')))
            ->assertRedirect(route('admin.orders.index'));

        $this->assertDatabaseMissing(Order::class, ['id' => $order->id]);

        foreach ($items as $item) {
            $this->assertDatabaseMissing(OrderItem::class, ['id' => $item->id]);
        }
    }

    #[Test]
    public function it_can_get_its_one_time_confirmation_template_when_its_a_preorder(): void
    {
        $pickup = Pickup::factory()->create(['settings' => []]);
        $order = Order::factory()->create(['pickup_id' => $pickup->id, 'status_id' => OrderStatus::preOrder()]);

        Setting::updateOrCreate(
            ['key' => 'email_order_confirmation_template'],
            ['value' => null]
        );

        $product = Product::factory()->create(['settings' => ['confirmation_email_template_id' => null]]);
        OrderItem::factory()->create(['order_id' => $order->id, 'product_id' => $product->id]);

        $this->assertNull($order->oneTimeConfirmationEmailTemplate());

        $global_template = Template::factory()->create();
        Setting::updateOrCreate(
            ['key' => 'email_order_confirmation_template'],
            ['value' => $global_template->id]
        );

        $order->refresh();

        $this->assertEquals($global_template->id, $order->oneTimeConfirmationEmailTemplate()?->id);

        $pickup_template = Template::factory()->create();
        $pickup->settings = ['email_order_confirmation_template' => $pickup_template->id];
        $pickup->save();

        $order->refresh();

        $this->assertEquals($pickup_template->id, $order->oneTimeConfirmationEmailTemplate()?->id);

        $product_template = Template::factory()->create();
        $product->settings = ['confirmation_email_template_id' => $product_template->id];
        $product->save();

        $order->refresh();

        $this->assertEquals($product_template->id, $order->oneTimeConfirmationEmailTemplate()?->id);

    }

    #[Test]
    public function it_can_get_its_one_time_confirmation_template_when_not_a_preorder(): void
    {
        $pickup = Pickup::factory()->create(['settings' => []]);
        $order = Order::factory()->create(['pickup_id' => $pickup->id, 'status_id' => OrderStatus::confirmed()]);

        $product_template = Template::factory()->create();

        Setting::updateOrCreate(
            ['key' => 'email_order_confirmation_template'],
            ['value' => null]
        );

        $product = Product::factory()->create(['settings' => ['confirmation_email_template_id' => $product_template->id]]);
        OrderItem::factory()->create(['order_id' => $order->id, 'product_id' => $product->id]);

        $this->assertNull($order->oneTimeConfirmationEmailTemplate());

        $global_template = Template::factory()->create();
        Setting::updateOrCreate(
            ['key' => 'email_order_confirmation_template'],
            ['value' => $global_template->id]
        );

        $order->refresh();

        $this->assertEquals($global_template->id, $order->oneTimeConfirmationEmailTemplate()?->id);

        $pickup_template = Template::factory()->create();
        $pickup->settings = ['email_order_confirmation_template' => $pickup_template->id];
        $pickup->save();

        $order->refresh();

        $this->assertEquals($pickup_template->id, $order->oneTimeConfirmationEmailTemplate()?->id);
    }

    #[Test]
    public function a_cancelled_orders_render_correct_view(): void
    {
        $order = Order::factory()->create(['confirmed' => true]);

        $order->cancel();

        $this->actingAsAdmin()
            ->get(route('admin.orders.edit', compact('order')))
            ->assertOk()
            ->assertViewIs('orders.show')
            ->assertViewHas('order', $order);

    }

    #[Test]
    public function a_cancelled_orders_tags_are_displayed(): void
    {
        $order = Order::factory()->create(['confirmed' => true]);

        $order->cancel();

        $this->actingAsAdmin()
            ->get(route('admin.orders.edit', compact('order')))
            ->assertOk()
            ->assertDontSee('Tags');

        $order_two = Order::factory()->create(['confirmed' => true]);
        $tag = Tag::factory()->create();
        $order_two->tags()->attach($tag->id);

        $order_two->cancel();

        $this->actingAsAdmin()
            ->get(route('admin.orders.edit', ['order' => $order_two]))
            ->assertOk()
            ->assertSee('Tags');
    }
}
