<?php

namespace Tests\Feature\Admin;

use App\Models\Order;
use App\Models\OrderFee;
use App\Models\User;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class OrderFeeTest extends TenantTestCase
{
    #[Test]
    public function an_unauthenticated_user_cannot_fetch_order_fees(): void
    {
        $order = Order::factory()->create();

        $this->get(route('admin.orders.fees.index', compact('order')))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function a_customer_cannot_fetch_order_fees(): void
    {
        $order = Order::factory()->create();

        $this->actingAsApiCustomer()
            ->get(route('admin.orders.fees.index', compact('order')))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function an_admin_can_fetch_order_fees(): void
    {
        $order = Order::factory()->create();
        OrderFee::factory(2)->for($order)->create();

        $this->actingAsAdmin()
            ->get(route('admin.orders.fees.index', compact('order')))
            ->assertJsonCount(2);
    }

    #[Test]
    public function an_unauthenticated_user_cannot_create_an_order_fee(): void
    {
        $order = Order::factory()->create();

        $this->post(route('admin.orders.fees.store', compact('order')))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function a_customer_cannot_create_an_order_fee(): void
    {
        $order = Order::factory()->create();

        $this->actingAsApiCustomer()
            ->post(route('admin.orders.fees.store', compact('order')))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function it_validates_order_fee_creation(): void
    {
        $order = Order::factory()->create();

        $this->actingAsAdmin()
            ->post(route('admin.orders.fees.store', compact('order')))
            ->assertRedirect()
            ->assertSessionHasErrors([
                'title' => 'The title field is required.',
                'qty' => 'The qty field is required.',
                'amount' => 'The amount field is required.'
            ]);

        $this->post(route('admin.orders.fees.store', compact('order')), [
            'qty' => 'a',
            'amount' => 'a'
        ])
            ->assertRedirect()
            ->assertSessionHasErrors([
                'qty' => 'The qty field must be an integer.',
                'amount' => 'The amount field must be a number.'
            ]);

        $this->post(route('admin.orders.fees.store', compact('order')), [
            'qty' => 0,
            'amount' => -0.01
        ])
            ->assertRedirect()
            ->assertSessionHasErrors([
                'qty' => 'The qty field must be at least 1.',
                'amount' => 'The amount field must be at least 0.'
            ]);
    }

    #[Test]
    public function an_admin_can_create_an_order_fee(): void
    {
        $order = Order::factory()->create();

        $admin = User::factory()->admin()->create();

        $this->actingAs($admin)
            ->post(route('admin.orders.fees.store', compact('order')), [
                'title' => 'some fee',
                'qty' => 2,
                'amount' => 10.01
            ])
            ->assertCreated()
            ->assertJsonStructure(['fee', 'order', 'responseText']);

        $this->assertDatabaseHas(OrderFee::class, [
            'user_id' => $admin->id,
            'type' => 'fixed',
            'title' => 'some fee',
            'qty' => 2,
            'amount' => 1001,
            'subtotal' => 2002
        ]);
    }

    #[Test]
    public function an_admin_cannot_create_a_fee_for_an_order_that_is_already_paid(): void
    {
        $order = Order::factory()->create(['paid' => true]);

        $this->actingAsAdmin()
            ->post(route('admin.orders.fees.store', compact('order')), [
                'title' => 'some fee',
                'qty' => 2,
                'amount' => 10.01
            ])
            ->assertBadRequest()
            ->assertJsonFragment(['A fee cannot be added to an order that is already paid.']);
    }

    #[Test]
    public function an_unauthenticated_user_cannot_update_an_order_fee(): void
    {
        $fee = OrderFee::factory()->create();

        $this->patch(route('admin.orders.fees.update', [$fee->order_id, $fee->id]))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function a_customer_cannot_update_an_order_fee(): void
    {
        $fee = OrderFee::factory()->create();

        $this->actingAsApiCustomer()
            ->patch(route('admin.orders.fees.update', [$fee->order_id, $fee->id]))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function it_validates_order_fee_updates(): void
    {
        $fee = OrderFee::factory()->create();

        $this->actingAsAdmin()
            ->patch(route('admin.orders.fees.update', [$fee->order_id, $fee->id]))
            ->assertRedirect()
            ->assertSessionHasErrors([
                'title' => 'The title field is required.',
                'qty' => 'The qty field is required.',
                'amount' => 'The amount field is required.'
            ]);

        $this->patch(route('admin.orders.fees.update', [$fee->order_id, $fee->id]), [
            'qty' => 'a',
            'amount' => 'a'
        ])
            ->assertRedirect()
            ->assertSessionHasErrors([
                'qty' => 'The qty field must be an integer.',
                'amount' => 'The amount field must be a number.'
            ]);

        $this->patch(route('admin.orders.fees.update', [$fee->order_id, $fee->id]), [
            'qty' => 0,
            'amount' => -0.01
        ])
            ->assertRedirect()
            ->assertSessionHasErrors([
                'qty' => 'The qty field must be at least 1.',
                'amount' => 'The amount field must be at least 0.'
            ]);
    }

    #[Test]
    public function an_admin_can_update_an_order_fee(): void
    {
        $fee = OrderFee::factory()->create();

        $admin = User::factory()->admin()->create();

        $this->actingAs($admin)
            ->patch(route('admin.orders.fees.update', [$fee->order_id, $fee->id]), [
                'title' => 'some fee',
                'qty' => 2,
                'amount' => 10.01
            ])
            ->assertOk()
            ->assertJsonStructure(['fee', 'order', 'responseText']);

        $this->assertDatabaseHas(OrderFee::class, [
            'id' => $fee->id,
            'title' => 'some fee',
            'qty' => 2,
            'amount' => 1001,
            'subtotal' => 2002
        ]);
    }

    #[Test]
    public function an_admin_cannot_update_a_fee_for_an_order_that_is_already_paid(): void
    {
        $order = Order::factory()->create(['paid' => true]);
        $fee = OrderFee::factory()->create(['order_id' => $order->id]);

        $this->actingAsAdmin()
            ->patch(route('admin.orders.fees.update', [$fee->order_id, $fee->id]), [
                'title' => 'some fee',
                'qty' => 2,
                'amount' => 10.01
            ])
            ->assertBadRequest()
            ->assertJsonFragment(['A fee cannot be updated on an order that is already paid.']);
    }

    #[Test]
    public function an_unauthenticated_user_cannot_delete_an_order_fee(): void
    {
        $fee = OrderFee::factory()->create();

        $this->delete(route('admin.orders.fees.destroy', [$fee->order_id, $fee->id]))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function a_customer_cannot_delete_an_order_fee(): void
    {
        $fee = OrderFee::factory()->create();

        $this->actingAsApiCustomer()
            ->delete(route('admin.orders.fees.destroy', [$fee->order_id, $fee->id]))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function an_admin_can_delete_an_order_fee(): void
    {
        $fee = OrderFee::factory()->create();

        $admin = User::factory()->admin()->create();

        $this->actingAs($admin)
            ->delete(route('admin.orders.fees.destroy', [$fee->order_id, $fee->id]), [
                'title' => 'some fee',
                'qty' => 2,
                'amount' => 10.01
            ])
            ->assertOk()
            ->assertJsonStructure(['order', 'responseText']);

        $this->assertDatabaseMissing(OrderFee::class, ['id' => $fee->id]);
    }

    #[Test]
    public function an_admin_cannot_delete_a_fee_for_an_order_that_is_already_paid(): void
    {
        $order = Order::factory()->create(['paid' => true]);
        $fee = OrderFee::factory()->create(['order_id' => $order->id]);

        $this->actingAsAdmin()
            ->delete(route('admin.orders.fees.destroy', [$fee->order_id, $fee->id]), [
                'title' => 'some fee',
                'qty' => 2,
                'amount' => 10.01
            ])
            ->assertBadRequest()
            ->assertJsonFragment(['A fee cannot be deleted from an order that is already paid.']);

        $this->assertDatabaseHas(OrderFee::class, ['id' => $fee->id]);

    }
}
