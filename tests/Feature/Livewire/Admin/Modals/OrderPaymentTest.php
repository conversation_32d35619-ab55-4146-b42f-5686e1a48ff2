<?php

namespace Tests\Feature\Livewire\Admin\Modals;

use App\Livewire\Admin\Modals\OrderPayment;
use App\Models\Card;
use App\Models\Order;
use App\Models\Payment;
use App\Models\User;
use Illuminate\Support\Str;
use Livewire\Livewire;
use PHPUnit\Framework\Attributes\Test;
use Tests\LivewireTenantTestCase;

class OrderPaymentTest extends LivewireTenantTestCase
{
    #[Test]
    public function it_can_render(): void
    {
        $customer = User::factory()->create();
        $payment = Payment::factory()->create();
        $card = Card::factory()->create(['user_id' => $customer->id]);

        $order = Order::factory()->create([
            'customer_id' => $customer->id,
            'payment_id' => $payment->id,
            'payment_source_id' => $card->id,
            'payment_date' => '2024-01-01',
            'due_date' => '2024-01-03',
            'payment_terms' => 'Net 30',
            'payment_notes' => 'Test notes',
            'paid' => true,
        ]);

        Livewire::test(OrderPayment::class)
            ->dispatch('open-modal-order-payment', $order->id)
            ->assertStatus(200)
            ->assertSet('order_id', $order->id)
            ->assertSet('user_id', $customer->id)
            ->assertSet('payment_method', $payment->id)
            ->assertSet('is_card', false)
            ->assertSet('payment_source', $card->source_id)
            ->assertSet('payment_date', '2024-01-01')
            ->assertSet('due_date', '2024-01-03')
            ->assertSet('payment_terms', 'Net 30')
            ->assertSet('payment_notes', 'Test notes')
            ->assertSet('paid', true);
    }

    #[Test]
    public function it_resets_source_id_when_payment_is_not_card(): void
    {
        $payment_one = Payment::factory()->create();
        $payment_two = Payment::factory()->create();
        $card = Card::factory()->create();

        $order = Order::factory()->create([
            'payment_id' => $payment_one->id,
            'payment_source_id' => $card->id,
        ]);

        Livewire::test(OrderPayment::class)
            ->dispatch('open-modal-order-payment', $order->id)
            ->assertStatus(200)
            ->assertSet('payment_method', $payment_one->id)
            ->assertSet('payment_source', $card->source_id)
            ->set('payment_method', $payment_two->id)
            ->assertNotSet('payment_source', $card->source_id);
    }

    #[Test]
    function it_validates_the_submit_request(): void
    {
        $order = Order::factory()->create();

        Livewire::test(OrderPayment::class)
            ->dispatch('open-modal-order-payment', $order->id)
            ->assertStatus(200)
            ->set('payment_method', 897123498)
            ->call('submit')
            ->assertHasErrors(['payment_method' => 'The selected payment method is invalid.'])
            ->set('payment_source', 'anc')
            ->call('submit')
            ->assertHasErrors(['payment_source' => 'The selected payment source is invalid.'])
            ->set('payment_date', '04-04-2024')
            ->call('submit')
            ->assertHasErrors(['payment_date' => 'The payment date field must match the format Y-m-d.'])
            ->set('due_date', '04-04-2024')
            ->call('submit')
            ->assertHasErrors(['due_date' => 'The due date field must match the format Y-m-d.'])
            ->set('payment_terms', Str::random(256))
            ->call('submit')
            ->assertHasErrors(['payment_terms' => 'The payment terms field must not be greater than 255 characters.'])
            ->set('payment_notes', Str::random(2001))
            ->call('submit')
            ->assertHasErrors(['payment_notes' => 'The payment notes field must not be greater than 2000 characters.']);
    }

    #[Test]
    function it_can_successfully_update_payment_details(): void
    {
        $payment = Payment::where('key', 'card')->first();

        if (is_null($payment)) {
            $payment = Payment::factory()->create(['key' => 'card']);
        }

        $order = Order::factory()->create([
            'payment_id' => 0,
            'payment_source_id' => null,
            'payment_date' => '2024-01-01',
            'due_date' => '2024-01-03',
            'payment_terms' => 'Net 30',
            'payment_notes' => 'Test notes',
            'paid' => false,
        ]);

        $card = Card::factory()->create(['user_id' => $order->customer_id]);

        Livewire::test(OrderPayment::class)
            ->dispatch('open-modal-order-payment', $order->id)
            ->assertStatus(200)
            ->set('payment_method', $payment->id)
            ->set('payment_source', $card->source_id)
            ->set('payment_date', '2024-02-02')
            ->set('due_date', '2024-02-05')
            ->set('payment_terms', 'Net 90')
            ->set('payment_notes', 'Updated notes')
            ->set('paid', true)
            ->call('submit')
            ->assertHasNoErrors()
            ->assertDispatched('admin-notification-sent')
            ->assertDispatched('order-updated', order_id: $order->id)
            ->assertSet('open', false);

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'payment_id' => $payment->id,
            'payment_source_id' => $card->id,
            'payment_date' => '2024-02-02',
            'due_date' => '2024-02-05',
            'payment_terms' => 'Net 90',
            'payment_notes' => 'Updated notes',
            'paid' => true,
        ]);
    }
}
