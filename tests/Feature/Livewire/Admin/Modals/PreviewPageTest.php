<?php

namespace Tests\Feature\Livewire\Admin\Modals;

use App\Livewire\Admin\Modals\PreviewPage;
use App\Models\Page;
use Livewire\Livewire;
use PHPUnit\Framework\Attributes\Test;
use Tests\LivewireTenantTestCase;

class PreviewPageTest extends LivewireTenantTestCase
{
    #[Test]
    public function it_can_render(): void
    {
        $page = Page::factory()->create();

        Livewire::test(PreviewPage::class, ['page_id' => $page->id])
            ->dispatch('open-modal-preview-page')
            ->assertStatus(200)
            ->assertSet('open', true)
            ->assertSet('page_id', $page->id);
    }

    #[Test]
    function it_can_close(): void
    {
        $page = Page::factory()->create();

        Livewire::test(PreviewPage::class, ['page_id' => $page->id])
            ->dispatch('open-modal-preview-page')
            ->assertStatus(200)
            ->assertSet('page_id', $page->id)
            ->assertSet('open', true)
            ->call('close')
            ->assertSet('open', false);
    }
}
