<?php

namespace Tests\Feature\Livewire\Admin;

use App\Livewire\Admin\SubscriptionOrders;
use App\Models\Pickup;
use App\Models\RecurringOrder;
use Livewire\Livewire;
use PHPUnit\Framework\Attributes\Test;
use Tests\LivewireTenantTestCase;

class SubscriptionOrdersTest extends LivewireTenantTestCase
{
    #[Test]
    public function the_component_can_render(): void
    {
        $subscription = RecurringOrder::factory()->create();

        Livewire::test(SubscriptionOrders::class, compact('subscription'))
            ->assertStatus(200);
    }

    #[Test]
    function fulfillment_id_is_validated_upon_submission(): void
    {
        $subscription = RecurringOrder::factory()->create();

        Livewire::test(SubscriptionOrders::class, compact('subscription'))
            ->set('fulfillment_id', null)
            ->call('saveDeliveryMethod')
            ->assertHasErrors(['fulfillment_id' => 'required'])
            ->set('fulfillment_id', '9999999999')
            ->call('saveDeliveryMethod')
            ->assertHasErrors(['fulfillment_id' => 'exists']);;
    }

    #[Test]
    function it_can_update_a_subscription_delivery_method(): void
    {
        $subscription = RecurringOrder::factory()->create();

        $pickup = Pickup::factory()->create();

        Livewire::test(SubscriptionOrders::class, compact('subscription'))
            ->set('fulfillment_id', $pickup->id)
            ->call('saveDeliveryMethod')
            ->assertHasNoErrors()
            ->assertDispatched('subscriptionUpdated');

        $this->assertDatabaseHas(RecurringOrder::class, [
            'id' => $subscription->id,
            'fulfillment_id' => $pickup->id,
        ]);
    }

    #[Test]
    function reorder_frequency_is_validated_upon_submission(): void
    {
        $subscription = RecurringOrder::factory()->create();

        Livewire::test(SubscriptionOrders::class, compact('subscription'))
            ->set('reorder_frequency', 0)
            ->call('saveFrequency')
            ->assertHasErrors(['reorder_frequency' => 'in']);
    }

    #[Test]
    function it_can_update_a_subscription_reorder_frequency(): void
    {
        $subscription = RecurringOrder::factory()->create();

        Livewire::test(SubscriptionOrders::class, compact('subscription'))
            ->set('reorder_frequency', 28)
            ->call('saveFrequency')
            ->assertHasNoErrors()
            ->assertDispatched('subscriptionUpdated');

        $this->assertDatabaseHas(RecurringOrder::class, [
            'id' => $subscription->id,
            'reorder_frequency' => 28,
        ]);
    }
}
