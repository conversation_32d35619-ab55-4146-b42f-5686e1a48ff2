<?php

namespace Tests\Feature\Reports;

use App\Models\Event;
use App\Models\User;
use Illuminate\Support\Collection;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class AccountCreditAppliedReportTest extends TenantTestCase
{
    #[Test]
    public function an_guest_cannot_fetch_the_account_credit_applied_report(): void
    {
        $this->get(route('admin.reports.account-credit-applied'))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function a_non_admin_cannot_fetch_the_account_credit_applied_report(): void
    {
        $this->actingAsCustomer()
            ->get(route('admin.reports.account-credit-applied'))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function an_admin_can_fetch_the_account_credit_applied_report(): void
    {
        $this->actingAsAdmin()
            ->get(route('admin.reports.account-credit-applied'))
            ->assertOk()
            ->assertViewIs('reports.account-credit-applied.index')
            ->assertViewHas([
                'savedFilters',
                'appliedFilters',
                'appliedFilter',
                'results',
            ]);
    }

    #[Test]
    public function the_report_can_be_filtered_by_applied_date(): void
    {
        $event_one = Event::factory()->create([
            'model_type' => User::class,
            'event_id' => 'credit_applied',
            'description' => 'Event one',
            'metadata' => ['amount' => 100],
            'created_at' => now()->subDays(8),
        ]);

        $event_two = Event::factory()->create([
            'model_type' => User::class,
            'event_id' => 'credit_applied',
            'description' => 'Event two',
            'metadata' => ['amount' => 100],
            'created_at' => now(),
        ]);

        // defaults to last 7 days
        $this->actingAsAdmin()
            ->get(route('admin.reports.account-credit-applied'))
            ->assertOk()
            ->assertViewHas('results', function (Collection $arg) use ($event_one, $event_two) {
                return $arg->doesntContain(fn($arg_event) => $arg_event['description'] == $event_one->description)
                    && $arg->contains(fn($arg_event) => $arg_event['description'] == $event_two->description);
            });

        $this->get(route('admin.reports.account-credit-applied', [
            'created_at' => [
                'start' => today()->format('M jS Y'),
                'end' => '',
            ]
        ]))
            ->assertOk()
            ->assertViewHas([
                'results' => function (Collection $arg) use ($event_one, $event_two) {
                    return $arg->doesntContain(fn($arg_event) => $arg_event['description'] === $event_one->description)
                        && $arg->contains(fn($arg_event) => $arg_event['description'] === $event_two->description);
                }
            ]);

        $this->get(route('admin.reports.account-credit-applied', [
            'created_at' => [
                'start' => '',
                'end' => today()->subDay()->format('M jS Y'),
            ]
        ]))
            ->assertOk()
            ->assertViewHas([
                'results' => function (Collection $arg) use ($event_one, $event_two) {
                    return $arg->contains(fn($arg_event) => $arg_event['description'] === $event_one->description)
                        && $arg->doesntContain(fn($arg_event) => $arg_event['description'] === $event_two->description);
                }
            ]);

        $this->get(route('admin.reports.account-credit-applied', [
            'created_at' => [
                'start' => today()->subDays(8)->format('M jS Y'),
                'end' => today()->format('M jS Y'),
            ]
        ]))
            ->assertOk()
            ->assertViewHas([
                'results' => function (Collection $arg) use ($event_one, $event_two) {
                    return $arg->contains(fn($arg_event) => $arg_event['description'] === $event_one->description)
                        && $arg->contains(fn($arg_event) => $arg_event['description'] === $event_two->description);
                }
            ]);

        $this->get(route('admin.reports.account-credit-applied', [
            'created_at' => [
                'start' => today()->subDays(10)->format('M jS Y'),
                'end' => today()->subDays(9)->format('M jS Y'),
            ]
        ]))
            ->assertOk()
            ->assertViewHas([
                'results' => function (Collection $arg) use ($event_one, $event_two) {
                    return $arg->doesntContain(fn($arg_event) => $arg_event['description'] === $event_one->description)
                        && $arg->doesntContain(fn($arg_event) => $arg_event['description'] === $event_two->description);
                }
            ]);
    }
}
