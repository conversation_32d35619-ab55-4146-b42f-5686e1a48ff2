<?php

namespace Tests\Feature\Theme\Store;

use App\Models\BundleProduct;
use App\Models\Cart;
use App\Models\Order;
use App\Models\Product;
use App\Models\ProductPrice;
use App\Models\ProductPriceGroup;
use App\Models\Setting;
use App\Models\User;
use App\Models\Vendor;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class DefaultStoreTest extends TenantTestCase
{
    public function setup(): void
    {
        parent::setup();

        // Disable the ZipGate feature.
        Setting::updateOrCreate(['key' => 'require_shopper_local'],['value' => false]);

        Setting::updateOrCreate(['key' => 'store_default_collection'], ['value' => null]);

        Product::query()->delete();
    }

    #[Test]
    public function it_can_load_the_configured_store_page_title(): void
    {
        Setting::updateOrCreate(['key' => 'require_shopper_local'],['value' => false]);

        Setting::updateOrCreate(['key' => 'store_page_title'], ['value' => 'Custom Title']);

        $this->get(route('store.index'))
            ->assertOk()
            ->assertViewHas('pageTitle', 'Custom Title');
    }

    #[Test]
    public function it_loads_store_index_page_variables(): void
    {
        Product::factory()->create();

        $this->get(route('store.index'))
            ->assertOk()
            ->assertSessionHas('store_url')
            ->assertViewIs('theme::store.index')
            ->assertViewHas('pageTitle', 'Shop Now')
            ->assertViewHas('heading', fn($value) => is_null($value))
            ->assertViewHas('subheading', fn($value) => is_null($value))
            ->assertViewHas('pageDescription', fn($value) => is_null($value))
            ->assertViewHas('headerPhoto', fn($value) => is_null($value))
            ->assertViewHas('tags', fn($value) => $value instanceof Collection)
            ->assertViewHas('pageCanonical', fn($value) => is_null($value))
            ->assertViewHas('products', function ($value) {
                return $value instanceof Paginator
                    && $value->count() === 1
                    && $value[0]->relationLoaded('bundle')
                    && $value[0]->relationLoaded('vendor')
                    && $value[0]->relationLoaded('price')
                    && $value[0]->relationLoaded('variants');
            });
    }

    #[Test]
    public function it_loads_the_expected_products_ordered_by_title_asc(): void
    {
        $product_one = Product::factory()->create(['title' => 'b']);
        $product_two = Product::factory()->create(['title' => 'a']);

        $this->get(route('store.index'))
            ->assertOk()
            ->assertViewHas('products', fn($value) => $value instanceof Paginator
                && $value->count() === 2
                && $value[0]->id = $product_two->id
                    && $value[1]->id = $product_one->id)
            ->assertSee($product_one->title)
            ->assertSee($product_two->title);
    }

    #[Test]
    public function it_loads_the_expected_product_bundle_relation(): void
    {
        $bundle_product = Product::factory()->create(['title' => 'bundle of something']);
        $product_in_bundle = Product::factory()->create(['title' => 'single product']);
        BundleProduct::factory()->create([
            'bundle_id' => $bundle_product->id,
            'product_id' => $product_in_bundle->id
        ]);

        $this->get(route('store.index'))
            ->assertOk()
            ->assertViewHas('products', fn($value) => $value[0] instanceof Product
                && $value[0]->id === $bundle_product->id
                && $value[0]->relationLoaded('bundle')
                && ! is_null($value[0]->bundle)
                && $value[0]->bundle instanceof EloquentCollection
                && $value[0]->bundle->count() === 1
                && $value[0]->bundle->first() instanceof Product
                && $value[0]->bundle->first()->id = $product_in_bundle->id);
    }

    #[Test]
    public function it_loads_the_expected_product_vendor_relation(): void
    {
        $vendor = Vendor::factory()->create();
        $product = Product::factory()->create(['vendor_id' => $vendor->id]);

        $this->get(route('store.index'))
            ->assertOk()
            ->assertViewHas('products', fn($value) => $value[0] instanceof Product
                && $value[0]->id === $product->id
                && $value[0]->relationLoaded('vendor')
                && ! is_null($value[0]->vendor)
                && $value[0]->vendor instanceof Vendor
                && $value[0]->vendor->id === $vendor->id
                && count($value[0]->vendor->toArray()) === 3
                && count(array_diff(array_keys($value[0]->vendor->toArray()), ['id', 'title', 'slug'])) === 0);
    }

    #[Test]
    public function it_loads_the_expected_product_price_relation_when_customer_has_no_price_group(): void
    {
        $product = Product::factory()->create();
        $price_group = ProductPriceGroup::factory()->create();
        ProductPrice::factory()->create(['product_id' => $product->id, 'group_id' => $price_group->id]);

        $this->get(route('store.index'))
            ->assertOk()
            ->assertViewHas('products', fn($value) => $value[0] instanceof Product
                && $value[0]->id === $product->id
                && $value[0]->relationLoaded('price')
                && is_null($value[0]->price));
    }

    #[Test]
    public function it_loads_the_expected_product_price_relation_when_customer_has_a_price_group(): void
    {
        $product = Product::factory()->create();
        $price_group = ProductPriceGroup::factory()->create();
        $customer = User::factory()->create(['pricing_group_id' => $price_group->id]);

        // set the current order for customer
        $order = Order::factory()->create(['customer_id' => $customer->id, 'pickup_id' => $customer->pickup_point]);
        

        $product_price = ProductPrice::factory()->create(['product_id' => $product->id, 'group_id' => $price_group->id]);

        $this->actingAs($customer)
            ->get(route('store.index'))
            ->assertOk()
            ->assertViewHas('products', fn($value) => $value[0] instanceof Product
                && $value[0]->id === $product->id
                && $value[0]->relationLoaded('price')
                && ! is_null($value[0]->price)
                && $value[0]->price instanceof ProductPrice
                && $value[0]->price->id === $product_price->id);
    }

    #[Test]
    public function it_does_not_load_the_product_price_relation_when_customer_has_an_unknown_price_group(): void
    {
        $product = Product::factory()->create();
        $price_group = ProductPriceGroup::factory()->create();
        $customer = User::factory()->create(['pricing_group_id' => 0]);

        // set the current order for customer
        $order = Order::factory()->create(['customer_id' => $customer->id, 'pickup_id' => $customer->pickup_point]);
        

        ProductPrice::factory()->create(['product_id' => $product->id, 'group_id' => $price_group->id]);

        $this->actingAs($customer)
            ->get(route('store.index'))
            ->assertOk()
            ->assertViewHas('products', fn($value) => $value[0] instanceof Product
                && $value[0]->id === $product->id
                && $value[0]->relationLoaded('price')
                && is_null($value[0]->price));
    }

    #[Test]
    public function it_loads_expected_products_ordered_by_custom_sort(): void
    {
        Setting::factory()->create(['key' => 'store_sort_order', 'value' => 'sku-desc']);

        $product_one = Product::factory()->create(['title' => 'b', 'sku' => 'a1']);
        $product_two = Product::factory()->create(['title' => 'a', 'sku' => 'b2']);

        $this->get(route('store.index'))
            ->assertOk()
            ->assertViewHas('products', fn($value) => $value instanceof Paginator
                && $value->count() === 2
                && $value[0]->id = $product_one->id
                && $value[1]->id = $product_one->id);
    }

    #[Test]
    public function it_loads_expected_products_ordered_by_title_asc_when_custom_sort_is_unknown(): void
    {
        Setting::factory()->create(['key' => 'store_sort_order', 'value' => 'skudesc']);

        $product_one = Product::factory()->create(['title' => 'b', 'sku' => 'a1']);
        $product_two = Product::factory()->create(['title' => 'a', 'sku' => 'b2']);

        $this->get(route('store.index'))
            ->assertOk()
            ->assertViewHas('products', fn($value) => $value instanceof Paginator
                && $value->count() === 2
                && $value[0]->id = $product_two->id
                && $value[1]->id = $product_one->id);
    }

    #[Test]
    public function it_does_not_load_products_excluded_by_order_pickup_location(): void
    {
        $customer = User::factory()->create();

        $cart = Cart::factory()->create(['shopper_type' => User::class, 'shopper_id' => $customer->id]);
        $cart->updateCartLocation($customer->pickup);

        $products = Product::factory()->count(2)->create();

        $customer->pickup->products()->attach($products->first()->id);

        $this->actingAs($customer)
            ->get(route('store.index'))
            ->assertOk()
            ->assertViewHas('products', fn($value) => $value instanceof Paginator
                && $value->count() === 1);
    }

    #[Test]
    public function it_loads_the_expected_product_variants_relation(): void
    {
        $product = Product::factory()->create(['title' => 'a']);
        $variant_product = Product::factory()->create(['title' => 'b']);
        $product->variants()->attach($variant_product->id);

        $this->get(route('store.index'))
            ->assertOk()
            ->assertViewHas('products', fn($value) => $value instanceof Paginator
                && $value->count() === 2
                && $value[0] instanceof Product
                && $value[0]->id === $product->id
                && $value[0]->relationLoaded('variants')
                && ! is_null($value[0]->variants)
                && $value[0]->variants instanceof EloquentCollection
                && $value[0]->variants->count() === 1
                && $value[0]->variants->first() instanceof Product
                && $value[0]->variants->first()->id === $variant_product->id);
    }

    #[Test]
    public function it_does_not_load_products_variants_excluded_by_order_pickup_location(): void
    {
        $customer = User::factory()->create();

        $cart = Cart::factory()->create(['shopper_type' => User::class, 'shopper_id' => $customer->id]);
        $cart->updateCartLocation($customer->pickup);

        $product = Product::factory()->create();
        $variant_product = Product::factory()->create();

        $product->variants()->attach($variant_product->id);
        $customer->pickup->products()->attach($variant_product->id);

        $this->actingAs($customer)
            ->get(route('store.index'))
            ->assertOk()
            ->assertViewHas('products', fn($value) => $value instanceof Paginator
                && $value->count() === 1
                && $value[0] instanceof Product
                && $value[0]->id === $product->id
                && $value[0]->relationLoaded('variants')
                && ! is_null($value[0]->variants)
                && $value[0]->variants instanceof EloquentCollection
                && $value[0]->variants->count() === 0);
    }

    #[Test]
    public function it_loads_the_expected_product_variants_price_group_relation(): void
    {
        $product = Product::factory()->create(['title' => 'a']);
        $variant_product = Product::factory()->create(['title' => 'b']);
        $product->variants()->attach($variant_product->id);

        $price_group = ProductPriceGroup::factory()->create();
        $customer = User::factory()->create(['pricing_group_id' => $price_group->id]);

        // set the current order for customer
        $order = Order::factory()->create(['customer_id' => $customer->id, 'pickup_id' => $customer->pickup_point]);
        

        $product_price = ProductPrice::factory()->create(['product_id' => $variant_product->id, 'group_id' => $price_group->id]);

        $this->actingAs($customer)
            ->get(route('store.index'))
            ->assertOk()
            ->assertViewHas('products', fn($value) => $value instanceof Paginator
                && $value->count() === 2
                && $value[0] instanceof Product
                && $value[0]->id === $product->id
                && $value[0]->relationLoaded('variants')
                && ! is_null($value[0]->variants)
                && $value[0]->variants instanceof EloquentCollection
                && $value[0]->variants->count() === 1
                && $value[0]->variants->first() instanceof Product
                && $value[0]->variants->first()->id === $variant_product->id
                && $value[0]->variants->first()->relationLoaded('price')
                && ! is_null($value[0]->variants->first()->price)
                && $value[0]->variants->first()->price instanceof ProductPrice
                && $value[0]->variants->first()->price->id === $product_price->id);
    }

    #[Test]
    public function it_does_not_load_products_when_not_visible(): void
    {
        $product_one = Product::factory()->create(['visible' => false]);
        $product_two = Product::factory()->create(['visible' => true]);

        $this->get(route('store.index'))
            ->assertOk()
            ->assertViewHas('products', fn($value) => $value instanceof Paginator
                && $value->count() === 1
                && $value[0]->id = $product_two->id);
    }

    #[Test]
    public function it_loads_the_expected_visible_products_and_their_expected_invisible_variants(): void
    {
        $product = Product::factory()->create(['title' => 'a', 'visible' => true]);
        $variant_product = Product::factory()->create(['title' => 'b', 'visible' => false]);
        $product->variants()->attach($variant_product->id);

        $this->get(route('store.index'))
            ->assertOk()
            ->assertViewHas('products', fn($value) => $value instanceof Paginator
                && $value->count() === 1
                && $value[0] instanceof Product
                && $value[0]->id === $product->id
                && $value[0]->relationLoaded('variants')
                && ! is_null($value[0]->variants)
                && $value[0]->variants instanceof EloquentCollection
                && $value[0]->variants->count() === 1
                && $value[0]->variants->first() instanceof Product
                && $value[0]->variants->first()->id === $variant_product->id);
    }

    #[Test]
    public function it_does_not_load_products_when_they_are_hidden_from_search(): void
    {
        // Full text searching doesn't work inside a database transaction.
        // Need to commit started transaction and truncate manually after test
        DB::commit();

        Product::factory()->create(['title' => 'abcd', 'hide_from_search' => true]);
        $product_two = Product::factory()->create(['title' => 'abcd', 'hide_from_search' => false]);

        $this->get(route('store.index', ['q' => 'abcd']))
            ->assertOk()
            ->assertViewHas('products', fn($value) => $value instanceof Paginator
                && $value->count() === 1
                && $value[0]->id === $product_two->id);

        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        Product::truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');
    }

    #[Test]
    public function it_filters_down_to_sale_products_when_sale_term_is_used(): void
    {
        config(['grazecart.sale_keywords' => ['saleterm', 'anotherterm']]);

        Product::factory()->create(['sale' => false]);
        $product_two = Product::factory()->create(['sale' => true]);

        $this->get(route('store.index', ['q' => 'saleterm']))
            ->assertOk()
            ->assertViewHas('products', fn($value) => $value instanceof Paginator
                && $value->count() === 1
                && $value[0]->id === $product_two->id);

        $this->get(route('store.index', ['q' => 'anotherterm']))
            ->assertOk()
            ->assertViewHas('products', fn($value) => $value instanceof Paginator
                && $value->count() === 1
                && $value[0]->id === $product_two->id);
    }

    #[Test]
    public function it_rank_filters_by_search_term(): void
    {
        // Full text searching doesn't work inside a database transaction.
        // Need to commit started transaction and truncate manually after test
        DB::commit();

        $product_one = Product::factory()->create(['title' => 'tenderloin', 'keywords' => 'shoulder']);
        $product_two = Product::factory()->create(['title' => 'shoulder']);
        Product::factory()->create(['title' => 'lettuce']);

        $this->get(route('store.index', ['q' => 'shoulder']))
            ->assertOk()
            ->assertViewHas('products', fn($value) => $value instanceof Paginator
                && $value->count() === 2
                && $value[0]->id === $product_two->id
                && $value[1]->id === $product_one->id);

        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        Product::truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');
    }
}
