<div class="panel panel-tabs">
    <div class="panel-body">
        {{--Location Street--}}
        <div class="form-group">
            <label for="title">Street</label>
            <input type="text" name="street" value="{{ $pickup->street }}" class="form-control">
        </div>

        {{--Location Street 2--}}
        <div class="form-group">
            <label for="title">Street Line 2</label>
            <input type="text" name="street_2" value="{{ $pickup->street_2 }}" class="form-control">
        </div>

        {{--Location City--}}
        <div class="form-group">
            <label for="title">City</label>
            <input type="text" name="city" value="{{ $pickup->city }}" class="form-control">
        </div>

        {{--Location State--}}
        <div class="form-group">
            <label for="state">State/Province</label>
            <x-form.state-select
                    class="form-control"
                    name="state"
                    :selected="$pickup->state"
            />
        </div>

        {{--Location Zip--}}
        <div class="form-group">
            <label for="zip">Zip</label>
            <input type="text" name="zip" class="form-control" value="{{ $pickup->zip }}" />
        </div>
    </div>
    <div class="panel-footer text-right">
        <button type="submit" class="btn btn-action" @click.prevent="submitForm('updateResourceForm')">Save</button>
    </div>
</div>

<div class="panel panel-tabs">
    <div class="panel-heading">Delivery Instructions</div>
    <div class="panel-body">
        <text-editor class="prose max-w-full" name="delivery_instructions" content="{{ old('delivery_instructions', $pickup->delivery_instructions) }}"></text-editor>
    </div>
    <div class="panel-footer text-right">
        <button type="submit" class="btn btn-action" @click.prevent="submitForm('updateResourceForm')">Save</button>
    </div>
</div>
