@props(['selected' => null, 'include_all' => false, 'placeholder' => null, 'sort' => null])

@php
    $selected_array = \Illuminate\Support\Arr::wrap($selected);
@endphp

<select {{ $attributes }}>
    @if($placeholder ?? false)
        <option value="">{{ $placeholder }}</option>
    @elseif($include_all)
        <option value="">All</option>
    @endif
    @foreach(\App\Models\Collection::when($sort, fn($query) => $query->orderBy('title', $sort))->pluck('title', 'id') as $id => $title)
        <option value="{{ $id }}" @selected(in_array($id, $selected_array))>{{ $title }}</option>
    @endforeach
</select>
