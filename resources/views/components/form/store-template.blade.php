@props(['selected' => null, 'placeholder' => null])

@php
    $options = [
        'default' => 'Default',
        'bundle' => 'Bundle',
        'standard' => 'Standard',
    ];
@endphp

<select {{ $attributes }}>
    @if($placeholder)
        <option value="">{{ $placeholder }}</option>
    @endif
    @foreach($options as $key => $label)
        <option value="{{ $key }}" @selected($key === $selected)>{{ $label }}</option>
    @endforeach
</select>

