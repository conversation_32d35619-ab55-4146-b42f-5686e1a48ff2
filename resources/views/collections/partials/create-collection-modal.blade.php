@component('partials.modal', ['id' => 'createCollectionModal'])
    @slot('header')
        Create a Collection
    @endslot

    @slot('body')
        <form action="/admin/collections" method="POST" id="storeCollectionForm">
        @csrf
        <div class="form-group">
            <label for="name">Collection Name</label>
            <input type="text" name="title" class="form-control"> 
        </div>

        <div class="checkbox">
            <label>
                <input type="checkbox" name="add-to-menu" checked> Show in Store Menu
            </label>
        </div>
    @endslot

    @slot('footer')
        <button type="button" class="btn btn-alt" @click="hideModal('createCollectionModal')">Cancel</button>
        <button 
            type="submit" 
            class="btn btn-action" 
            @click="submitForm('storeCollectionForm')">Create Collection</button>
        </form>
    @endslot
@endcomponent()