{{--Process Order Button--}}
@if(setting('process_order_show'))
    <div class="flex-item mr-xs">
        @if(!$order->processed)
            <form action="/admin/orders/{{ $order->id }}/process" method="POST" style="display: inline-block;"
                  id="processOrderForm">
                @csrf
                <button type="submit" class="btn btn-action" @click="submitForm('processOrderForm', $event)">Process
                </button>
            </form>
        @else
            <button type="button" class="btn btn-default" disabled>Processed <i class="fas fa-check"></i></button>
        @endif
    </div>
@endif

<div class="flex-item mr-xs">
    @php($paginator = app(\App\Services\OrderPaginationService::class))

    {!! $paginator->show($order->id) !!}
</div>

{{--Print Options--}}
<div class="flex-item mr-xs">
    <div class="dropdown">
        <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true"
                aria-expanded="false">
            <i class="fas fa-print"></i> Print <i class="fas fa-caret-down"></i>
        </button>
        <ul class="dropdown-menu pull-right">
            <li><a href="/admin/orders/{{ $order->id }}/print/invoice" target="_blank">Print Invoice</a></li>
            <li><a href="/admin/orders/{{ $order->id }}/print/packing" target="_blank">Print Packing List</a></li>
            <li class="divider"></li>
            <li><a href="/admin/orders/{{ $order->id }}/print/shipping-label" target="_blank">Print Shipping Label</a>
            </li>
            <li><a href="/admin/orders/{{ $order->id }}/print/packing-label" target="_blank">Print Packing Label</a>
            </li>
        </ul>
    </div>
</div>

<div class="flex-item mr-xs">
    {{--Notification Options--}}
    <button type="button" class="btn btn-default" @click="showModal('notifyModal')">
        <i class="fas fa-envelope"></i> Notify
    </button>
</div>

{{--Actions Menu--}}
<div class="flex-item">
    <div class="dropdown">
        <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true"
                aria-expanded="false">
            <i class="fas fa-gear"></i> Actions <i class="fas fa-caret-down"></i>
        </button>

        <ul class="dropdown-menu pull-right">
            @if( ! $order->isFromBlueprint())
                <li><a href="#" @click="showModal('reorderModal')">Reorder</a></li>
            @endif
            <li class="divider"></li>
            <li><a href="#" @click="showModal('orderEventsModal')">Show Event Log</a></li>
            @if($order->processed)
                <li x-data><a href="#" x-on:click="$dispatch('open-modal-order-replacement', { order_id: {{ $order->id }} })">Replace Order</a></li>
            @endif
            <li><a href="#" @click="showModal('cancelOrderModal')">Cancel Order</a></li>
            @if(!$order->is_paid)
                <li><a href="#" @click="showModal('deleteOrderModal')">Delete Order</a></li>
            @endif
        </ul>
    </div>
</div>
