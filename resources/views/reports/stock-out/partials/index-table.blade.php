@include('partials.saved-filters')
<div class="panel">
    <div class="panel-heading">
        <form action="/admin/reports/stock-out" method="GET" id="filterReportsForm" class="hidden-print">
            <input type="hidden" name="field" value="{{ request()->get('field', 'inventory') }}">
            <div class="flex align-items-m">
                <button
                        type="button"
                        class="btn btn-white flex-item br-right-0 br--l"
                        @click.stop="showPanel('filterPanel')"
                        tabindex="1"
                >Filter
                    <span class="hide-mobile">Report</span>
                    <i class="fas fa-caret-down"></i></button>
                <div class="flex-item-fill">
                    <button type="submit" class="btn btn-clear btn-input-overlay"><i class="fas fa-search"></i></button>
                    <input
                            tabindex="1"
                            type="text"
                            class="form-control input-overlay-left br--r"
                            placeholder="Search products by name..."
                            name="products"
                            value="{{ Request::get('products') }}"
                    >
                </div>
            </div>
            @include('products.partials.filter-form')
        </form>
        @include('partials.applied-filters', ['filter_resource' => 'product'])
    </div>
    <div class="panel-body pa-0">
        <div class="table-responsive">
            <table class="table table-striped table-full table-hover">
                <thead>
                <tr>
                    <th>{!! sortTable('Product', 'title') !!}</th>
                    <th>{!! sortTable('Stock-out', 'stock_out_inventory') !!}</th>
                    <th>{!! sortTable('Inventory', 'inventory') !!}</th>
                </tr>
                </thead>
                <tbody>
                @foreach($results as $result)
                    <tr>
                        <td style="max-width: 100px;">
                            <a href="{{ route('admin.products.edit', [$result->id]) }}">{{ $result->title }}</a>

                            <div class="text-gray-medium mt-xs mb-xs fs-sm">{{ $result->vendor['title'] ?? '' }}</div>
                            <span class="label label-light _{!! (int) !$result->visible !!}">Hidden</span>
                            <span class="label label-danger _{!! (int) $result->trashed() !!}">Deleted</span>
                        </td>
                        <td data-label="On Order">{{ $result['stock_out_inventory'] }}</td>
                        <td data-label="Inventory">{{ $result['inventory'] }}</td>
                    </tr>
                @endforeach
                </tbody>
                @if(!count($results))
                    <tr>
                        <td colspan="100%"><h4>No results found.</h4></td>
                    </tr>
                @endif
            </table>
        </div>
    </div>
</div>

<div class="text-center">
    <p>Showing {{ $results->count() }} of {{ $results->total() }} result(s)</p>
    {!! $results->appends(request()->all())->render() !!}
</div>
