@include('partials.saved-filters')
<div class="panel">
    <div class="panel-heading">
        <form action="/admin/reports/inventory" method="GET" id="filterReportsForm" class="hidden-print">
            <input type="hidden" name="field" value="{{ request()->get('field', 'inventory') }}">
            <div class="flex align-items-m">
                <button
                        type="button"
                        class="btn btn-white flex-item br-right-0 br--l"
                        @click.stop="showPanel('filterPanel')"
                        tabindex="1"
                >Filter
                    <span class="hide-mobile">Report</span>
                    <i class="fas fa-caret-down"></i></button>
                <div class="flex-item-fill">
                    <button type="submit" class="btn btn-clear btn-input-overlay"><i class="fas fa-search"></i></button>
                    <input
                            tabindex="1"
                            type="text"
                            class="form-control input-overlay-left br--r"
                            placeholder="Search products by name..."
                            name="products"
                            value="{{ Request::get('products') }}"
                    >
                </div>
            </div>
            @include('reports.inventory.partials.filter-form')
        </form>
        @include('partials.applied-filters', ['filter_resource' => 'inventory_report'])
    </div>
    <div class="panel-body pa-0">
        <div class="table-responsive">
            <table class="table table-striped table-full table-hover">
                <thead>
                <tr>
                    <th>{!! sortTable('Product', 'title') !!}</th>
                    <th>
                        <span
                                title="Products of confirmed orders not picked-up"
                                class="tooltip"
                        >
                            {!! sortTable('On Order', 'count_on_order') !!}

                        </span>
                    </th>
                    @if(Request::get('delivery_date'))
                        <th>
                            <span
                                    title="Products of unconfirmed orders not picked-up"
                                    class="tooltip"
                            >
                                {!! sortTable('Upcoming', 'upcoming') !!}
                            </span>
                        </th>
                    @endif
                    <th>
                        <span class="tooltip" title="Inventory available for online purchase.">
                            {!! sortTable('Online', 'inventory') !!}
                        </span>
                    </th>
                    <th>
                        <span class="tooltip" title="Any inventory kept at your processor">
                            {!! sortTable('Processor', 'processor_inventory') !!}
                        </span>
                    </th>
                    <th>
                        <span class="tooltip" title="Any extra inventory not including online or processor">
                            {!! sortTable('Other', 'other_inventory') !!}
                        </span>
                    </th>
                    <th>{!! sortTable('Total', 'total_inventory') !!}</th>
                    <th class="text-right">{!! sortTable('Item Cost', 'item_cost') !!}</th>
                    <th class="text-right">{!! sortTable('Subtotal', 'subtotal') !!}</th>
                </tr>
                <tr>
                    <td></td>
                    <td><strong>{{ $results->sum('count_on_order') }}</strong></td>
                    @if(Request::get('delivery_date'))
                        <td><strong>{{ $results->sum('upcoming') }}</strong></td>
                    @endif
                    <td><strong>{{ $results->sum('inventory') }}</strong></td>
                    <td><strong>{{ $results->sum('processor_inventory') }}</strong></td>
                    <td><strong>{{ $results->sum('other_inventory') }}</strong></td>
                    <td><strong>{{ $results->sum('total_inventory') }}</strong></td>
                    <td></td>
                    <td class="text-right"><strong>&#36;{{ number_format($results->sum('subtotal'), 2) }}</strong></td>
                </tr>
                </thead>
                <tbody>
                @foreach($results as $result)
                    <tr>
                        <td width="350"><a href="{{ route('admin.products.edit', $result->id) }}">{{ $result['title'] }}</a></td>
                        <td data-label="On Order">{{ $result['count_on_order'] }}</td>
                        @if(Request::get('delivery_date'))
                            <td data-label="Upcoming">{{ $result['upcoming'] ?? '0' }}</td>
                        @endif
                        <td data-label="Inventory">{{ $result['inventory'] }}</td>
                        <td data-label="Processor Inventory">{{ $result['processor_inventory'] }}</td>
                        <td data-label="Other Inventory">{{ $result['other_inventory'] }}</td>
                        <td data-label="Total Inventory">{{ $result['total_inventory'] }}</td>
                        <td data-label="Item Cost" class="text-right">&#36;{{ money($result->cost()) }}</td>
                        <td data-label="Subtotal" class="text-right">&#36;{{ number_format($result['subtotal'], 2) }}</td>
                    </tr>
                @endforeach
                </tbody>
                @if(!count($results))
                    <tr>
                        <td colspan="100%"><h4>No results found.</h4></td>
                    </tr>
                @endif
            </table>
        </div>
    </div>
</div>
