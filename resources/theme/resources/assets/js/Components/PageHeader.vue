<template>
    <header class="tw-font-semibold tw-text-gray-700 tw-shadow"
            :class="[
                'tw-relative tw-bg-theme-header-bg-color',
                theme.hasBorderOnHeader ? 'tw-border-theme-header-border-color' : null,
                theme.hasTopBorderOnHeader ? 'tw-border-t' : 'tw-border-b'
            ]"
            :style="{
                borderTopWidth: theme.hasTopBorderOnHeader ? theme.headerBorderSize : '0px',
                borderBottomWidth: theme.hasBottomBorderOnHeader ? theme.headerBorderSize : '0px',
            }"
    >
        <div class="tw-max-w-7xl tw-mx-auto tw-py-8 tw-px-4 sm:tw-px-6 lg:tw-px-8">
            <div class="tw-relative tw-flex tw-items-center">
                <a href="/" class="tw-absolute tw-left-0 tw-top-1/2 tw--mt-6">
                    <span class="tw-sr-only" v-text="settings.farm_name"></span>
                    <img v-if="theme.hasLogo" :src="theme.logo_src" class="tw-h-12 tw-w-auto" :alt="`${settings.farm_name}`"/>
                    <h2 v-else v-text="settings.farm_name"></h2>
                </a>
            </div>
        </div>
    </header>
</template>

<script>
import { useThemeStore } from "../stores/theme";
import { useTenantStore } from "../stores/tenant";
import { ChevronRightIcon } from "@heroicons/vue/solid";

export default {
    name: "PageHeader",

    components: { ChevronRightIcon },

    setup() {
        const theme = useThemeStore()
        const settings = useTenantStore()

        const steps = [
            { name: 'Store', href: '/store', status: 'complete' },
            { name: 'Cart', href: '/cart', status: 'current' },
            { name: 'Checkout', href: '#', status: 'upcoming' },
        ]

        return {
            theme,
            settings,
            steps
        }
    }
};
</script>
