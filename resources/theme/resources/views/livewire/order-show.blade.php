@php
    /** @var \App\Models\Order $order */
    $delivery_method = $order->pickup
@endphp

<div class="tw-reset tw-bg-white">
    <div class="tw-mx-auto tw-max-w-2xl tw-px-4 tw-pt-16 tw-pb-24 sm:tw-px-6 lg:tw-max-w-7xl lg:tw-px-8">

        <div class="sm:tw-flex sm:tw-items-center sm:tw-justify-between">
            <div class="tw-flex tw-items-center">
                <h1 class="tw-text-3xl tw-font-bold tw-tracking-tight tw-text-gray-900 sm:tw-text-4xl">Order #{{ $order->id }}</h1>

                @if($has_subscription && $order->isFromBlueprint())
                    <span class="tw-ml-2 tw-inline-flex tw-items-center tw-rounded-md tw-bg-theme-brand-color/10 tw-px-2.5 tw-py-0.5 tw-text-sm tw-font-medium tw-text-theme-brand-color/80">
                        <svg class="tw--ml-0.5 tw-mr-1.5 tw-h-3 tw-w-3 tw-theme-brand-color/40" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 4.5c1.215 0 2.417.055 3.604.162a.68.68 0 01.615.597c.124 1.038.208 2.088.25 3.15l-1.689-1.69a.75.75 0 00-1.06 1.061l2.999 3a.75.75 0 001.06 0l3.001-3a.75.75 0 10-1.06-1.06l-1.748 1.747a41.31 41.31 0 00-.264-3.386 2.18 2.18 0 00-1.97-1.913 41.512 41.512 0 00-7.477 0 2.18 2.18 0 00-1.969 1.913 41.16 41.16 0 00-.16 ********** 0 101.495.12c.041-.52.093-1.038.154-1.552a.68.68 0 01.615-.597A40.012 40.012 0 0110 4.5zM5.281 9.22a.75.75 0 00-1.06 0l-3.001 3a.75.75 0 101.06 1.06l1.748-1.747c.042 1.141.13 2.27.264 3.386a2.18 2.18 0 001.97 1.913 41.533 41.533 0 007.477 0 2.18 2.18 0 001.969-1.913c.064-.534.117-1.071.16-1.61a.75.75 0 10-1.495-.12c-.041.52-.093 1.037-.154 1.552a.68.68 0 01-.615.597 40.013 40.013 0 01-7.208 0 .68.68 0 01-.615-.597 39.785 39.785 0 01-.25-3.15l1.689 1.69a.75.75 0 001.06-1.061l-2.999-3z" clip-rule="evenodd"/>
                        </svg>
                        Subscription
                    </span>
                @endif
            </div>
            @if( ! $order->isFromBlueprint())
                <button type="button" wire:click="cancel" class="tw-mt-1 sm:tw-mt-0 sm:tw-ml-3 tw-flex tw-items-center tw-justify-center tw-rounded-md tw-border tw-border-gray-300 tw-bg-white tw-px-2.5 tw-py-2 tw-text-sm tw-font-medium tw-text-gray-700 tw-shadow-sm hover:tw-bg-gray-50 focus:tw-outline-none focus:tw-ring-2 focus:tw-ring-theme-action-color/50 focus:tw-ring-offset-2">
                    <span>Cancel Order</span>
                    <span class="tw-sr-only">{{ $order->id }}</span>
                </button>
            @endif
        </div>
        <div class="tw-mt-2 tw-space-y-2">
            <div class="tw-mt-2 tw-flex tw-items-center tw-text-sm tw-text-gray-500">
                <svg class="tw-mr-1.5 tw-h-4 tw-w-4 tw-flex-shrink-0 tw-text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fill-rule="evenodd" d="M5.75 2a.75.75 0 01.75.75V4h7V2.75a.75.75 0 011.5 0V4h.25A2.75 2.75 0 0118 6.75v8.5A2.75 2.75 0 0115.25 18H4.75A2.75 2.75 0 012 15.25v-8.5A2.75 2.75 0 014.75 4H5V2.75A.75.75 0 015.75 2zm-1 5.5c-.69 0-1.25.56-1.25 1.25v6.5c0 .69.56 1.25 1.25 1.25h10.5c.69 0 1.25-.56 1.25-1.25v-6.5c0-.69-.56-1.25-1.25-1.25H4.75z" clip-rule="evenodd"/>
                </svg>
                {{ $delivery_method->isDeliveryZone() ? 'Delivery' : 'Pickup' }} on {{ $order->pickupDatetime()?->format('M jS') ?? 'TBA' }}
                @if($delivery_method->isPickup())
                    at&nbsp;<a href="{{ url('https://maps.app.goo.gl/kDytccoKkbVL98UR7') }}" class="tw-text-theme-link-color">Seven Sons Farms</a>
                @endif
            </div>

            @if ($order->canBeModified())
                <div class="tw-mt-2 tw-flex tw-items-center tw-text-sm tw-text-gray-500">
                    <svg class="tw-mr-1.5 tw-h-4 tw-w-4 tw-flex-shrink-0 tw-text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>

                    Edit until {{ $order->deadlineDatetime()->format('M d | h:iA') }}
                </div>
            @endif
        </div>
        <div class="tw-mt-6 lg:tw-grid lg:tw-grid-cols-12 lg:tw-items-start lg:tw-gap-x-12 xl:tw-gap-x-16">
            <section aria-labelledby="cart-heading" class="lg:tw-col-span-7">
                @if( ! $order->pickupHasPassed() && $order->hasUnfulfilledItems())
                    <x-theme::unfulfilled-order-item-alert :order="$order"/>
                @endif

                <h2 id="cart-heading" class="tw-sr-only">Items in your order</h2>
                <livewire:theme.order-items :order="$order"/>
            </section>

            <!-- Order summary -->
            <section aria-labelledby="summary-heading" class="tw-mt-16 tw-rounded-lg tw-bg-gray-50 tw-px-4 tw-py-6 sm:tw-p-6 lg:tw-col-span-5 lg:tw-mt-0 lg:tw-p-8">
                <h2 id="summary-heading" class="tw-text-lg tw-font-medium tw-text-gray-900">Order summary</h2>

                <div class="tw-mt-6">
                    <x-theme::order-summary :order="$order"/>

                    <div class="tw-mt-6 tw-flex tw-items-center tw-justify-between tw-border-t tw-border-gray-200 tw-pt-4">
                        <dt class="tw-text-base tw-font-medium tw-text-gray-900">Total</dt>
                        <dd class="tw-text-base tw-font-medium tw-text-gray-900">${{ money($order->total) }}</dd>
                    </div>
                </div>

                <div class="tw-mt-6 tw-flex tw-justify-center tw-text-center tw-text-sm tw-text-gray-500">
                    <a href="{{ route('store.index') }}" class="tw-m-0 tw-w-full tw-no-underline">
                        <button type="button" @click="open = false" class="tw-block tw-w-full  tw-px-3 tw-py-2 tw-rounded-lg tw-text-sm tw-font-semibold btn btn-brand">
                            {{ __('messages.cart.keep_shopping') }}
                            <span aria-hidden="true"> &rarr;</span>
                        </button>
                    </a>
                </div>
            </section>
        </div>
    </div>
</div>

