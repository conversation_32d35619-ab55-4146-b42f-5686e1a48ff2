<div class="tw-py-4 tw-px-6 tw-text-gray-700">
    <header class="tw-block tw-text-center ">
        <h1 class="tw-mx-0 tw-mt-0 tw-mb-3 tw-text-3xl tw-font-medium tw-font-display">
            We're not in {{ $address_parts['city'] }} {{ $address_parts['state'] }}.
        </h1>
        <div class="tw-mx-auto">
            Enter your email to be informed as soon as we are in this area.
        </div>
    </header>

    <form wire:submit.prevent="submitLead" method="POST" class="tw-mt-5">
        @csrf
        <div class="tw-hidden">
            <input type="text" wire:model="username" autocomplete="off" tabindex="-1" class="tw-hidden">
            <input type="text" wire:model="timestamp" autocomplete="off" tabindex="-1" class="tw-hidden">
        </div>

        <div>
            <label for="email" class="tw-block tw-text-left tw-text-sm tw-font-medium tw-leading-6 tw-text-gray-900">@lang('Email address')</label>
            <div x-data x-init="$refs.email.focus()" class="tw-relative tw-rounded-md tw-shadow-sm">
                <input type="email" x-ref="email" wire:model="email" id="email" required class="tw-block tw-w-full tw-rounded-md tw-border-0 tw-py-1.5 tw-text-gray-900 tw-ring-1 tw-ring-inset tw-ring-gray-300 placeholder:tw-text-gray-400 focus:tw-ring-2 focus:tw-ring-inset focus:tw-ring-theme-action-color sm:tw-text-sm sm:tw-leading-6 @error('email') tw-pr-10 tw-text-red-900 tw-ring-red-300 placeholder:tw-text-red-300 focus:tw-ring-red-500 @enderror" placeholder="@lang('Email address')" @error('email') aria-invalid="true" aria-describedby="email-error" @enderror>
                @error('email')
                <div class="tw-pointer-events-none tw-absolute tw-inset-y-0 tw-right-0 tw-flex tw-items-center tw-pr-3">
                    <svg class="tw-h-5 tw-w-5 tw-text-red-500" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                @enderror
            </div>
            @error('email') <p class="tw-mt-2 tw-text-left tw-text-sm tw-text-red-600" id="email-error">{{ $message }}</p> @enderror
        </div>

        <div class="tw-mt-4">
            @include('theme::_partials.legal-links', ['message' => 'By submitting this form you agree to our'])
        </div>

        <div class="tw-mt-4">
            <button type="submit" wire:click.prevent="submitLead" wire:loading.attr="disabled" wire:loading.class="tw-cursor-not-allowed tw-opacity-75" wire:target="submitLead" class="tw-w-full tw-rounded-md tw-bg-theme-action-color tw-px-3 tw-py-2 tw-text-base tw-text-white tw-shadow-sm hover:tw-bg-theme-action-color focus-visible:tw-outline focus-visible:tw-outline-2 focus-visible:tw-outline-offset-2 focus-visible:tw-outline-theme-action-color">
                Notify Me
                <span wire:loading.inline wire:target="submitLead">...</span>
            </button>
        </div>
    </form>

    <div class="tw-mt-4 tw-text-center">
        <button type="submit" wire:click.prevent="tryAnotherPostalCode" wire:loading.attr="disabled" wire:loading.class="tw-cursor-not-allowed tw-opacity-75" wire:target="tryAnotherPostalCode" class="tw-text-base tw-text-theme-link-color hover:tw-text-theme-link-color/70">
            Try another postal code
            <span wire:loading.inline wire:target="tryAnotherPostalCode">...</span>
        </button>
    </div>
</div>
