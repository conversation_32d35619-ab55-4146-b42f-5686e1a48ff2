@php
    /** @var \App\Models\Product $product */
@endphp

@extends('theme::_layouts.main', [
	'pageTitle' => $product->getMetaTitle(),
	'pageDescription' => $product->getMetaDescription(),
	'pageCanonical' => $product->getCanonicalUrl(),
	'robots' => $product->robots(),
    'show_announcement_bar' => false,
])

@section('pageMetaTags')
    {!! $product->getHeadTags() !!}
    <meta property="og:title" content="{{ $product->getMetaTitle() }}">
    <meta property="og:site_name" content="{{ setting('farm_name') }}">
    <meta property="og:type" content="product">
    <meta property="og:url" content="{{ url()->current() }}">
    <meta property="og:image" content="{{ \App\Models\Media::s3ToCloudfront($product->mainPhoto?->path) }}">
    <meta property="og:description" content="{{ $product->getMetaDescription() }}">
@endsection

@section('content')
    @php
        $template = $product->setting('store_template', 'default');
    @endphp

    @if($template === 'standard')
        @include("theme::store.products.standard.show", [
            'product' => $product,
            'order' => $openOrder,
            'cart' => $cart,
            'has_subscription' => $has_subscription
        ])
    @elseif($template === 'bundle')
        @include("theme::store.products.bundles.show", [
            'product' => $product,
            'order' => $openOrder,
            'cart' => $cart,
            'has_subscription' => $has_subscription
        ])
    @else
        @include("theme::store.products.default", [
                'product' => $product,
                'order' => $openOrder,
                'cart' => $cart,
                'has_subscription' => $has_subscription
            ])
    @endif

@endsection

@push('scripts')
    @if($template === 'bundle')
        @vite([
            'resources/assets/js/theme/products/bundle.js',
        ])
    @elseif($template === 'standard')
        @vite([
            'resources/assets/js/theme/products/standard.js',
        ])
    @endif
    <script>
        if (typeof fbq !== 'undefined') {
            fbq('track', 'ViewContent', {
                content_type: 'product',
                content_ids: ['{{ $product->id }}'],
                content_name: '{{ $product->title }}'
            });
        }

        if (typeof gtag == 'function') {
            gtag('event', 'view_item', {
                currency: 'USD',
                value: {{ $product->getPrice() / 100 }},
                items: [
                    {
                        item_id: "{{ $product->id }}",
                        item_name: "{{ $product->title }}",
                        index: 0,
                        item_category: "{{ $product->category?->parentCategory?->name ?? $product->category?->name }}",
                        item_category2: "{{ ! is_null($product->category?->parentCategory) ? $product->category?->name : null }}",
                        item_list_id: 'product_details_page',
                        item_list_name: 'Product Details Page',
                        price: {{ $product->getPrice() / 100 }},
                        quantity: 1
                    }
                ]
            });
        }
    </script>
    {!! $product->getBodyTags() !!}
@endpush

